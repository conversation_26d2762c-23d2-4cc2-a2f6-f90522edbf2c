// 药品类别枚举
export enum MedicineCategory {
  CY = 'CY', // 草药
  XY = 'XY', // 西药
  ZCY = 'ZCY', // 中成药
}

/**
 * 打印类型枚举
 */
export enum PrintType {
  CHINESE_MEDICINE = 'CHINESE_MEDICINE', // 中成药处方
  MEDICAL_RECORD = 'MEDICAL_RECORD', // 病历
  REGISTRATION_RECORD = 'REGISTRATION_RECORD', // 登记记录
  TCM_PRESCRIPTION = 'TCM_PRESCRIPTION', // 中药处方
  TREATMENT_PROJECT = 'TREATMENT_PROJECT', // 诊疗项目
  WESTERN_MEDICINE = 'WESTERN_MEDICINE', // 西药处方
}

// 就诊状态枚举
export enum MedicalRecordStatus {
  CANCELLED = 'CANCELLED', // 已取消
  COMPLETED = 'COMPLETED', // 已完成
  DECOCTING = 'DECOCTING', // 煎药中
  DISPENSING = 'DISPENSING', // 取药中
  IN_PROGRESS = 'IN_PROGRESS', // 就诊中
  PRESCRIPTION_DONE = 'PRESCRIPTION_DONE', // 处方已开
  WAITING = 'WAITING', // 待接诊
}

// 枚举对应的中文标签
export const MedicalRecordStatusLabel: Record<MedicalRecordStatus, string> = {
  [MedicalRecordStatus.WAITING]: '待接诊',
  [MedicalRecordStatus.IN_PROGRESS]: '就诊中',
  [MedicalRecordStatus.PRESCRIPTION_DONE]: '已开方',
  [MedicalRecordStatus.DISPENSING]: '取药中',
  [MedicalRecordStatus.DECOCTING]: '煎药中',
  [MedicalRecordStatus.COMPLETED]: '已完成',
  [MedicalRecordStatus.CANCELLED]: '已取消',
};

/**
 * 抽屉类型
 */
export enum DrawerType {
  EDIT = 'edit',
  INFO = 'info',
}
