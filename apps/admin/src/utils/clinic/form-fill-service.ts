import type { Ref } from 'vue';

import type { MedicalRecordVO } from '#/api/clinic/medical/record/model';
import type { MedicalTemplateVO } from '#/api/clinic/medical/template/model';
import type { MedicinePrescriptionData } from '#/components/clinic/medicine-prescription-table';
import type { HerbalPrescriptionData } from '#/components/clinic/tcm-prescription-table';
import type { TreatmentProjectData } from '#/components/clinic/treatment-project-table';

import { HistoryDataParser } from './history-data-parser';

/**
 * 填充操作类型枚举
 */
export enum AutofillType {
  ALL = 'all',
  CHINESE_MEDICINE = 'chinese-medicine',
  DIAGNOSIS = 'diagnosis',
  TCM_PRESCRIPTION = 'tcm',
  TREATMENT_PROJECT = 'treatment',
  WESTERN_MEDICINE = 'western-medicine',
}

/**
 * 填充结果接口
 */
export interface FillResult {
  success: boolean;
  filledForms: string[];
  errors: string[];
  skippedForms: string[];
}

/**
 * 表单引用接口
 */
export interface FormRefs {
  medicalTemplate: Ref<MedicalTemplateVO>;
  herbalPrescriptionFormData: Ref<HerbalPrescriptionData>;
  chineseMedicinePrescriptionFormData: Ref<MedicinePrescriptionData>;
  westernMedicinePrescriptionFormData: Ref<MedicinePrescriptionData>;
  treatmentProjectFormData: Ref<TreatmentProjectData>;
}

/**
 * 表单组件引用接口
 */
export interface ComponentRefs {
  medicalRecordCard?: Ref<any>;
  tcmPrescriptionCard?: Ref<any>;
  chineseMedicinePrescriptionCard?: Ref<any>;
  westernMedicinePrescriptionCard?: Ref<any>;
  treatmentProjectCard?: Ref<any>;
}

/**
 * 表单填充服务
 * 负责将解析后的历史数据填充到对应的表单中
 */
export class FormFillService {
  private componentRefs: ComponentRefs;
  private confirmCallback?: (
    type: AutofillType,
    hasExistingData: boolean,
  ) => Promise<boolean>;
  private formRefs: FormRefs;

  constructor(
    formRefs: FormRefs,
    componentRefs: ComponentRefs = {},
    confirmCallback?: (
      type: AutofillType,
      hasExistingData: boolean,
    ) => Promise<boolean>,
  ) {
    this.formRefs = formRefs;
    this.componentRefs = componentRefs;
    this.confirmCallback = confirmCallback;
  }

  /**
   * 获取填充类型的中文名称
   * @param type 填充类型
   * @returns 中文名称
   */
  static getTypeName(type: AutofillType): string {
    const typeNames = {
      [AutofillType.ALL]: '全部数据',
      [AutofillType.DIAGNOSIS]: '病历诊断',
      [AutofillType.TCM_PRESCRIPTION]: '中药处方',
      [AutofillType.CHINESE_MEDICINE]: '中成药处方',
      [AutofillType.WESTERN_MEDICINE]: '西药处方',
      [AutofillType.TREATMENT_PROJECT]: '诊疗项目',
    };

    return typeNames[type] || type;
  }

  /**
   * 一键填充所有表单
   * @param record 历史就诊记录
   * @returns 填充结果
   */
  async fillAllForms(record: MedicalRecordVO): Promise<FillResult> {
    const result: FillResult = {
      success: true,
      filledForms: [],
      errors: [],
      skippedForms: [],
    };

    // 填充病历诊断
    if (record.diagnosisJson) {
      const diagnosisData = HistoryDataParser.parseDiagnosisData(
        record.diagnosisJson,
      );
      if (diagnosisData) {
        const success =
          await this.fillDiagnosisFormWithoutConfirm(diagnosisData);
        if (success) {
          result.filledForms.push('病历诊断');
        } else {
          result.errors.push('病历诊断填充失败');
        }
      } else {
        result.skippedForms.push('病历诊断（数据格式错误）');
      }
    } else {
      result.skippedForms.push('病历诊断（无数据）');
    }

    // 填充中药处方
    if (record.tcmPrescriptionJson) {
      const tcmData = HistoryDataParser.parseTcmPrescriptionData(
        record.tcmPrescriptionJson,
      );
      if (tcmData) {
        const success =
          await this.fillTcmPrescriptionFormWithoutConfirm(tcmData);
        if (success) {
          result.filledForms.push('中药处方');
        } else {
          result.errors.push('中药处方填充失败');
        }
      } else {
        result.skippedForms.push('中药处方（数据格式错误）');
      }
    } else {
      result.skippedForms.push('中药处方（无数据）');
    }

    // 填充中成药处方
    if (record.tcmpPrescriptionJson) {
      const chineseMedicineData = HistoryDataParser.parseChineseMedicineData(
        record.tcmpPrescriptionJson,
      );
      if (chineseMedicineData) {
        const success =
          await this.fillChineseMedicineFormWithoutConfirm(chineseMedicineData);
        if (success) {
          result.filledForms.push('中成药处方');
        } else {
          result.errors.push('中成药处方填充失败');
        }
      } else {
        result.skippedForms.push('中成药处方（无数据）');
      }
    } else {
      result.skippedForms.push('中成药处方（无数据）');
    }

    // 填充西药处方
    if (record.wmPrescriptionJson) {
      const westernMedicineData = HistoryDataParser.parseWesternMedicineData(
        record.wmPrescriptionJson,
      );
      if (westernMedicineData) {
        const success =
          await this.fillWesternMedicineFormWithoutConfirm(westernMedicineData);
        if (success) {
          result.filledForms.push('西药处方');
        } else {
          result.errors.push('西药处方填充失败');
        }
      } else {
        result.skippedForms.push('西药处方（数据格式错误）');
      }
    } else {
      result.skippedForms.push('西药处方（无数据）');
    }

    // 填充诊疗项目
    if (record.treatmentItemJson) {
      const treatmentData = HistoryDataParser.parseTreatmentData(
        record.treatmentItemJson,
      );
      if (treatmentData) {
        const success =
          await this.fillTreatmentFormWithoutConfirm(treatmentData);
        if (success) {
          result.filledForms.push('诊疗项目');
        } else {
          result.errors.push('诊疗项目填充失败');
        }
      } else {
        result.skippedForms.push('诊疗项目（数据格式错误）');
      }
    } else {
      result.skippedForms.push('诊疗项目（无数据）');
    }

    // 判断整体是否成功
    result.success =
      result.errors.length === 0 && result.filledForms.length > 0;

    return result;
  }

  /**
   * 填充中成药处方表单
   * @param data 中成药处方数据
   * @returns 是否填充成功
   */
  async fillChineseMedicineForm(
    data: MedicinePrescriptionData,
  ): Promise<boolean> {
    try {
      // 检查当前表单是否有数据
      const hasExistingData = this.checkExistingChineseMedicineData();

      if (hasExistingData && this.confirmCallback) {
        const confirmed = await this.confirmCallback(
          AutofillType.CHINESE_MEDICINE,
          true,
        );
        if (!confirmed) return false;
      }

      // 填充数据
      this.formRefs.chineseMedicinePrescriptionFormData.value = { ...data };

      // 触发组件更新
      if (this.componentRefs.chineseMedicinePrescriptionCard?.value) {
        // 如果组件有特定的更新方法，可以在这里调用
      }

      return true;
    } catch (error) {
      console.error('Failed to fill chinese medicine form:', error);
      return false;
    }
  }

  /**
   * 填充病历诊断表单
   * @param data 病历诊断数据
   * @returns 是否填充成功
   */
  async fillDiagnosisForm(data: MedicalTemplateVO): Promise<boolean> {
    try {
      // 检查当前表单是否有数据
      const hasExistingData = this.checkExistingDiagnosisData();

      if (hasExistingData && this.confirmCallback) {
        const confirmed = await this.confirmCallback(
          AutofillType.DIAGNOSIS,
          true,
        );
        if (!confirmed) return false;
      }

      // 填充数据
      this.formRefs.medicalTemplate.value = { ...data };

      // 触发组件更新
      if (this.componentRefs.medicalRecordCard?.value) {
        // 如果组件有特定的更新方法，可以在这里调用
      }

      return true;
    } catch (error) {
      console.error('Failed to fill diagnosis form:', error);
      return false;
    }
  }

  /**
   * 填充中药处方表单
   * @param data 中药处方数据
   * @returns 是否填充成功
   */
  async fillTcmPrescriptionForm(
    data: HerbalPrescriptionData,
  ): Promise<boolean> {
    try {
      // 检查当前表单是否有数据
      const hasExistingData = this.checkExistingTcmData();

      if (hasExistingData && this.confirmCallback) {
        const confirmed = await this.confirmCallback(
          AutofillType.TCM_PRESCRIPTION,
          true,
        );
        if (!confirmed) return false;
      }

      // 填充数据
      this.formRefs.herbalPrescriptionFormData.value = { ...data };

      // 触发组件更新
      if (this.componentRefs.tcmPrescriptionCard?.value) {
        // 如果组件有特定的更新方法，可以在这里调用
      }

      return true;
    } catch (error) {
      console.error('Failed to fill TCM prescription form:', error);
      return false;
    }
  }

  /**
   * 填充诊疗项目表单
   * @param data 诊疗项目数据
   * @returns 是否填充成功
   */
  async fillTreatmentForm(data: TreatmentProjectData): Promise<boolean> {
    try {
      // 检查当前表单是否有数据
      const hasExistingData = this.checkExistingTreatmentData();

      if (hasExistingData && this.confirmCallback) {
        const confirmed = await this.confirmCallback(
          AutofillType.TREATMENT_PROJECT,
          true,
        );
        if (!confirmed) return false;
      }

      // 填充数据
      this.formRefs.treatmentProjectFormData.value = { ...data };

      // 触发组件更新
      if (this.componentRefs.treatmentProjectCard?.value) {
        // 如果组件有特定的更新方法，可以在这里调用
      }

      return true;
    } catch (error) {
      console.error('Failed to fill treatment form:', error);
      return false;
    }
  }

  /**
   * 填充西药处方表单
   * @param data 西药处方数据
   * @returns 是否填充成功
   */
  async fillWesternMedicineForm(
    data: MedicinePrescriptionData,
  ): Promise<boolean> {
    try {
      // 检查当前表单是否有数据
      const hasExistingData = this.checkExistingWesternMedicineData();

      if (hasExistingData && this.confirmCallback) {
        const confirmed = await this.confirmCallback(
          AutofillType.WESTERN_MEDICINE,
          true,
        );
        if (!confirmed) return false;
      }

      // 填充数据
      this.formRefs.westernMedicinePrescriptionFormData.value = { ...data };

      // 触发组件更新
      if (this.componentRefs.westernMedicinePrescriptionCard?.value) {
        // 如果组件有特定的更新方法，可以在这里调用
      }

      return true;
    } catch (error) {
      console.error('Failed to fill western medicine form:', error);
      return false;
    }
  }

  /**
   * 检查是否有任何现有数据
   */
  private checkAnyExistingData(): boolean {
    return (
      this.checkExistingDiagnosisData() ||
      this.checkExistingTcmData() ||
      this.checkExistingChineseMedicineData() ||
      this.checkExistingWesternMedicineData() ||
      this.checkExistingTreatmentData()
    );
  }

  /**
   * 检查中成药处方是否有现有数据
   */
  private checkExistingChineseMedicineData(): boolean {
    const data = this.formRefs.chineseMedicinePrescriptionFormData.value;
    if (!data) return false;
    return (
      (data.tableData && data.tableData.length > 0) ||
      (data.prescription && data.prescription.trim() !== '')
    );
  }

  /**
   * 检查病历诊断是否有现有数据
   */
  private checkExistingDiagnosisData(): boolean {
    const template = this.formRefs.medicalTemplate.value;
    if (!template || !template.fieldList) return false;

    return template.fieldList.some(
      (field) =>
        field.fieldValue &&
        typeof field.fieldValue === 'string' &&
        field.fieldValue.trim() !== '',
    );
  }

  /**
   * 检查中药处方是否有现有数据
   */
  private checkExistingTcmData(): boolean {
    const data = this.formRefs.herbalPrescriptionFormData.value;
    if (!data) return false;
    return (
      (data.tableData && data.tableData.length > 0) ||
      (data.prescription && data.prescription.trim() !== '')
    );
  }

  /**
   * 检查诊疗项目是否有现有数据
   */
  private checkExistingTreatmentData(): boolean {
    const data = this.formRefs.treatmentProjectFormData.value;
    if (!data) return false;
    return (
      (data.tableData && data.tableData.length > 0) ||
      (data.prescription && data.prescription.trim() !== '')
    );
  }

  /**
   * 检查西药处方是否有现有数据
   */
  private checkExistingWesternMedicineData(): boolean {
    const data = this.formRefs.westernMedicinePrescriptionFormData.value;
    if (!data) return false;
    return (
      (data.tableData && data.tableData.length > 0) ||
      (data.prescription && data.prescription.trim() !== '')
    );
  }

  /**
   * 填充中成药处方表单（不需要确认）
   * @param data 中成药处方数据
   * @returns 是否填充成功
   */
  private async fillChineseMedicineFormWithoutConfirm(
    data: MedicinePrescriptionData,
  ): Promise<boolean> {
    try {
      // 直接填充数据，不需要确认
      this.formRefs.chineseMedicinePrescriptionFormData.value = { ...data };

      // 触发组件更新
      if (this.componentRefs.chineseMedicinePrescriptionCard?.value) {
        // 如果组件有特定的更新方法，可以在这里调用
      }

      return true;
    } catch (error) {
      console.error('Failed to fill chinese medicine form:', error);
      return false;
    }
  }

  /**
   * 填充病历诊断表单（不需要确认）
   * @param data 病历诊断数据
   * @returns 是否填充成功
   */
  private async fillDiagnosisFormWithoutConfirm(
    data: MedicalTemplateVO,
  ): Promise<boolean> {
    try {
      // 直接填充数据，不需要确认
      this.formRefs.medicalTemplate.value = { ...data };

      // 触发组件更新
      if (this.componentRefs.medicalRecordCard?.value) {
        // 如果组件有特定的更新方法，可以在这里调用
      }

      return true;
    } catch (error) {
      console.error('Failed to fill diagnosis form:', error);
      return false;
    }
  }

  /**
   * 填充中药处方表单（不需要确认）
   * @param data 中药处方数据
   * @returns 是否填充成功
   */
  private async fillTcmPrescriptionFormWithoutConfirm(
    data: HerbalPrescriptionData,
  ): Promise<boolean> {
    try {
      // 直接填充数据，不需要确认
      this.formRefs.herbalPrescriptionFormData.value = { ...data };

      // 触发组件更新
      if (this.componentRefs.tcmPrescriptionCard?.value) {
        // 如果组件有特定的更新方法，可以在这里调用
      }

      return true;
    } catch (error) {
      console.error('Failed to fill TCM prescription form:', error);
      return false;
    }
  }

  /**
   * 填充诊疗项目表单（不需要确认）
   * @param data 诊疗项目数据
   * @returns 是否填充成功
   */
  private async fillTreatmentFormWithoutConfirm(
    data: TreatmentProjectData,
  ): Promise<boolean> {
    try {
      // 直接填充数据，不需要确认
      this.formRefs.treatmentProjectFormData.value = { ...data };

      // 触发组件更新
      if (this.componentRefs.treatmentProjectCard?.value) {
        // 如果组件有特定的更新方法，可以在这里调用
      }

      return true;
    } catch (error) {
      console.error('Failed to fill treatment form:', error);
      return false;
    }
  }

  /**
   * 填充西药处方表单（不需要确认）
   * @param data 西药处方数据
   * @returns 是否填充成功
   */
  private async fillWesternMedicineFormWithoutConfirm(
    data: MedicinePrescriptionData,
  ): Promise<boolean> {
    try {
      // 直接填充数据，不需要确认
      this.formRefs.westernMedicinePrescriptionFormData.value = { ...data };

      // 触发组件更新
      if (this.componentRefs.westernMedicinePrescriptionCard?.value) {
        // 如果组件有特定的更新方法，可以在这里调用
      }

      return true;
    } catch (error) {
      console.error('Failed to fill western medicine form:', error);
      return false;
    }
  }
}
