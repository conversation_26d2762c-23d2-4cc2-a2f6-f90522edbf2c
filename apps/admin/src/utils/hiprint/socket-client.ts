import type {
  ClientInfo,
  PrinterInfo,
  SocketCallbacks,
  SocketConfig,
} from './types';

import { io, Socket } from 'socket.io-client';

import { SocketStatus } from './types';

/**
 * HiPrint Socket 客户端工具类
 */
export class HiPrintSocketClient {
  private callbacks: SocketCallbacks;
  private clientInfo: ClientInfo | null = null;
  private config: SocketConfig;
  private printerList: PrinterInfo[] = [];
  private socket: null | Socket = null;
  private status: SocketStatus = SocketStatus.DISCONNECTED;

  constructor(config: SocketConfig, callbacks: SocketCallbacks = {}) {
    this.config = {
      transports: ['websocket'],
      autoConnect: true,
      reconnectionAttempts: 5,
      reconnectionDelay: 1000,
      ...config,
    };
    this.callbacks = callbacks;
  }

  /**
   * 连接到 HiPrint 服务
   */
  connect(): void {
    if (this.socket?.connected) {
      console.warn('Socket is already connected');
      return;
    }

    this.status = SocketStatus.CONNECTING;

    this.socket = io(this.config.url, {
      transports: this.config.transports,
      autoConnect: this.config.autoConnect,
      reconnectionAttempts: this.config.reconnectionAttempts,
      reconnectionDelay: this.config.reconnectionDelay,
      auth: {
        token: this.config.token,
      },
    });

    this.setupEventListeners();
  }

  /**
   * 断开连接
   */
  disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
    this.status = SocketStatus.DISCONNECTED;
    this.clientInfo = null;
    this.printerList = [];
  }

  /**
   * 发送自定义事件
   */
  emit(event: string, data?: any): void {
    if (this.socket?.connected) {
      this.socket.emit(event, data);
    } else {
      console.warn('Socket is not connected');
    }
  }

  /**
   * 获取客户端信息
   */
  getClientInfo(): ClientInfo | null {
    return this.clientInfo;
  }

  /**
   * 获取打印机列表
   */
  getPrinterList(): PrinterInfo[] {
    return this.printerList;
  }

  /**
   * 获取 Socket 实例（用于高级操作）
   */
  getSocket(): null | Socket {
    return this.socket;
  }

  /**
   * 获取连接状态
   */
  getStatus(): SocketStatus {
    return this.status;
  }

  /**
   * 检查是否已连接
   */
  isConnected(): boolean {
    return this.socket?.connected ?? false;
  }

  /**
   * 移除事件监听
   */
  off(event: string, callback?: (...args: any[]) => void): void {
    if (this.socket) {
      this.socket.off(event, callback);
    }
  }

  /**
   * 监听自定义事件
   */
  on(event: string, callback: (...args: any[]) => void): void {
    if (this.socket) {
      this.socket.on(event, callback);
    }
  }

  /**
   * 主动刷新打印机列表
   */
  refreshPrinterList(): void {
    if (this.socket?.connected) {
      this.socket.emit('refreshPrinterList');
    } else {
      console.warn('Socket is not connected');
    }
  }

  /**
   * 主动请求客户端信息
   */
  requestClientInfo(): void {
    if (this.socket?.connected) {
      this.socket.emit('getClientInfo');
    } else {
      console.warn('Socket is not connected');
    }
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    if (!this.socket) return;

    // 连接成功
    this.socket.on('connect', () => {
      this.status = SocketStatus.CONNECTED;
      console.log('HiPrint socket connected');
      this.callbacks.onConnect?.();
    });

    // 连接断开
    this.socket.on('disconnect', (reason: string) => {
      this.status = SocketStatus.DISCONNECTED;
      console.log('HiPrint socket disconnected:', reason);
      this.callbacks.onDisconnect?.(reason);
    });

    // 连接错误
    this.socket.on('connect_error', (error: Error) => {
      this.status = SocketStatus.ERROR;
      console.error('HiPrint socket connection error:', error);
      this.callbacks.onError?.(error);
    });

    // 客户端信息
    this.socket.on('clientInfo', (clientInfo: ClientInfo) => {
      this.clientInfo = clientInfo;
      console.log('Received client info:', clientInfo);
      this.callbacks.onClientInfo?.(clientInfo);
    });

    // 打印机列表
    this.socket.on('printerList', (printerList: PrinterInfo[]) => {
      this.printerList = printerList;
      console.log('Received printer list:', printerList);
      this.callbacks.onPrinterList?.(printerList);
    });
  }
}

// 导出 SocketStatus 枚举
export { SocketStatus } from './types';
