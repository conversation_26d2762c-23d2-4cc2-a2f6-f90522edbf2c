/**
 * 客户端信息接口
 */
export interface ClientInfo {
  /** 主机名 */
  hostname: string;
  /** 客户端版本 */
  version: string;
  /** 平台类型 */
  platform: string;
  /** 系统架构 */
  arch: string;
  /** MAC 地址 */
  mac: string;
  /** 设备 IP */
  ip: string;
  /** 设备 IPv6 */
  ipv6: string;
  /** 本地服务地址 */
  clientUrl: string;
  /** 设备唯一 ID */
  machineId: string;
  /** 友好昵称 */
  nickName: string;
}

/**
 * 打印机选项接口
 */
export interface PrinterOptions {
  /** 打印机位置 */
  'printer-location': string;
  /** 打印机制造商和型号 */
  'printer-make-and-model': string;
  /** 系统驱动信息 */
  system_driverinfo: string;
}

/**
 * 打印机信息接口
 */
export interface PrinterInfo {
  /** 描述 */
  description: string;
  /** 显示名称 */
  displayName: string;
  /** 是否为默认打印机 */
  isDefault: boolean;
  /** 打印机名称 */
  name: string;
  /** 打印机选项 */
  options: PrinterOptions;
  /** 状态 */
  status: number;
}

/**
 * Socket 连接配置接口
 */
export interface SocketConfig {
  /** 服务器地址 */
  url: string;
  /** 认证令牌 */
  token: string;
  /** 传输方式 */
  transports?: string[];
  /** 自动连接 */
  autoConnect?: boolean;
  /** 重连尝试次数 */
  reconnectionAttempts?: number;
  /** 重连延迟 */
  reconnectionDelay?: number;
}

/**
 * Socket 事件回调接口
 */
export interface SocketCallbacks {
  /** 连接成功回调 */
  onConnect?: () => void;
  /** 连接断开回调 */
  onDisconnect?: (reason: string) => void;
  /** 连接错误回调 */
  onError?: (error: Error) => void;
  /** 客户端信息回调 */
  onClientInfo?: (clientInfo: ClientInfo) => void;
  /** 打印机列表回调 */
  onPrinterList?: (printerList: PrinterInfo[]) => void;
}

/**
 * Socket 连接状态枚举
 */
export enum SocketStatus {
  /** 已连接 */
  CONNECTED = 'connected',
  /** 连接中 */
  CONNECTING = 'connecting',
  /** 未连接 */
  DISCONNECTED = 'disconnected',
  /** 连接错误 */
  ERROR = 'error',
}
