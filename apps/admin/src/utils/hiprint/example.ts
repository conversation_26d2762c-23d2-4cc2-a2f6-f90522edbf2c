/**
 * HiPrint Socket 客户端使用示例
 */

import type { ClientInfo, PrinterInfo, SocketCallbacks } from './index';

import { computed, readonly } from 'vue';

import {
  DEFAULT_HIPRINT_CONFIG,
  HiPrintSocketClient,
  SocketStatus,
} from './index';

/**
 * 基础使用示例
 */
export function basicUsageExample() {
  // 创建回调函数
  const callbacks: SocketCallbacks = {
    onConnect: () => {
      console.log('✅ 连接成功');
    },
    onDisconnect: (reason) => {
      console.log('❌ 连接断开:', reason);
    },
    onError: (error) => {
      console.error('🚫 连接错误:', error);
    },
    onClientInfo: (clientInfo: ClientInfo) => {
      console.log('📱 客户端信息:', clientInfo);
      // 可以将客户端信息存储到全局状态或 Pinia store
      globalThis.clientInfo = clientInfo;
    },
    onPrinterList: (printerList: PrinterInfo[]) => {
      console.log('🖨️ 打印机列表:', printerList);
      // 可以将打印机列表存储到全局状态或 Pinia store
      globalThis.printerList = printerList;
    },
  };

  // 创建客户端实例
  const client = new HiPrintSocketClient(DEFAULT_HIPRINT_CONFIG, callbacks);

  // 连接
  client.connect();

  return client;
}

/**
 * 自定义配置示例
 */
export function customConfigExample() {
  const customConfig = {
    url: 'http://*************:17521', // 自定义服务器地址
    token: 'my-custom-token',
    transports: ['websocket'],
    autoConnect: false, // 手动连接
    reconnectionAttempts: 10,
    reconnectionDelay: 2000,
  };

  const callbacks: SocketCallbacks = {
    onConnect: () => {
      console.log('自定义配置连接成功');
      // 连接成功后主动请求信息
      client.requestClientInfo();
      client.refreshPrinterList();
    },
  };

  const client = new HiPrintSocketClient(customConfig, callbacks);

  // 手动连接
  setTimeout(() => {
    client.connect();
  }, 1000);

  return client;
}

/**
 * Vue 组合式 API 使用示例
 */
export function useHiPrintSocket() {
  let client: HiPrintSocketClient | null = null;

  const clientInfo = ref<ClientInfo | null>(null);
  const printerList = ref<PrinterInfo[]>([]);
  const connectionStatus = ref<SocketStatus>(SocketStatus.DISCONNECTED);

  const callbacks: SocketCallbacks = {
    onConnect: () => {
      connectionStatus.value = SocketStatus.CONNECTED;
    },
    onDisconnect: () => {
      connectionStatus.value = SocketStatus.DISCONNECTED;
    },
    onError: () => {
      connectionStatus.value = SocketStatus.ERROR;
    },
    onClientInfo: (info) => {
      clientInfo.value = info;
    },
    onPrinterList: (list) => {
      printerList.value = list;
    },
  };

  const connect = () => {
    if (!client) {
      client = new HiPrintSocketClient(DEFAULT_HIPRINT_CONFIG, callbacks);
    }
    client.connect();
  };

  const disconnect = () => {
    if (client) {
      client.disconnect();
      client = null;
    }
  };

  const refreshPrinters = () => {
    client?.refreshPrinterList();
  };

  const getClientInfo = () => {
    client?.requestClientInfo();
  };

  // 组件卸载时断开连接
  onUnmounted(() => {
    disconnect();
  });

  return {
    clientInfo: readonly(clientInfo),
    printerList: readonly(printerList),
    connectionStatus: readonly(connectionStatus),
    connect,
    disconnect,
    refreshPrinters,
    getClientInfo,
    isConnected: computed(
      () => connectionStatus.value === SocketStatus.CONNECTED,
    ),
  };
}

/**
 * 类式组件使用示例
 */
export class HiPrintManager {
  private client: HiPrintSocketClient;
  private clientInfo: ClientInfo | null = null;
  private printerList: PrinterInfo[] = [];

  constructor() {
    const callbacks: SocketCallbacks = {
      onConnect: this.handleConnect.bind(this),
      onDisconnect: this.handleDisconnect.bind(this),
      onClientInfo: this.handleClientInfo.bind(this),
      onPrinterList: this.handlePrinterList.bind(this),
    };

    this.client = new HiPrintSocketClient(DEFAULT_HIPRINT_CONFIG, callbacks);
  }

  public connect() {
    this.client.connect();
  }

  public disconnect() {
    this.client.disconnect();
  }

  public getClientInfo(): ClientInfo | null {
    return this.clientInfo;
  }

  public getPrinterList(): PrinterInfo[] {
    return this.printerList;
  }

  public refreshPrinters() {
    this.client.refreshPrinterList();
  }

  private handleClientInfo(clientInfo: ClientInfo) {
    this.clientInfo = clientInfo;
  }

  private handleConnect() {
    console.log('HiPrint 连接成功');
  }

  private handleDisconnect(reason: string) {
    console.log('HiPrint 连接断开:', reason);
  }

  private handlePrinterList(printerList: PrinterInfo[]) {
    this.printerList = printerList;
  }
}
