/**
 * HiPrint 工具模块
 * 提供 Socket.IO 连接和打印相关功能
 */

export { HiPrintSocketClient } from './socket-client';
export type {
  ClientInfo,
  PrinterInfo,
  PrinterOptions,
  SocketCallbacks,
  SocketConfig,
} from './types';
export { SocketStatus } from './types';

// 默认配置
export const DEFAULT_HIPRINT_CONFIG = {
  url: 'http://localhost:17521',
  token: 'vue-plugin-hiprint',
  transports: ['websocket'],
  autoConnect: true,
  reconnectionAttempts: 5,
  reconnectionDelay: 1000,
} as const;
