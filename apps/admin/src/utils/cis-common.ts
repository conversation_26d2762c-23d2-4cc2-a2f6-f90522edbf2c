import { h } from 'vue';

import { message } from 'ant-design-vue';

/**
 * 判断是否为真
 * @param value 值
 * @returns 是否为真
 */
export function isTrue(value: boolean | number | string) {
  return value === '1' || value === 1 || value === true;
}

/**
 * 校验表格编辑数据
 * @param tableErrMap 表格编辑数据错误映射
 * @returns 是否校验通过
 */
export function validTableEditRules(tableErrMap: any, tableName: string = '') {
  if (!tableErrMap || Object.keys(tableErrMap).length === 0) {
    return true;
  }
  const errMsgArray = Object.values(tableErrMap)[0] as any[];
  const errMsgObj = errMsgArray[0];
  const rowIndexText = String((errMsgObj?.rowIndex ?? 0) + 1);
  const columnTitleText = String(errMsgObj?.column?.title ?? '');
  message.open({
    type: 'error',
    content: h('span', [
      `请填写`,
      h('strong', { class: 'text-danger mx-1' }, tableName),
      `第`,
      h('strong', { class: 'text-danger mx-1' }, rowIndexText),
      '行',
      h('strong', { class: 'text-danger mx-1' }, `${columnTitleText}`),
      '字段信息',
    ]),
  });
  return false;
}

/**
 * 将分转换为元
 * @param price 价格
 * @returns 价格
 */
export function priceToYuan(price: number | string) {
  return (Number(price) / 100).toFixed(2) || '0.00';
}

export function yuanToPrice(price: number | string) {
  return Number(price) * 100;
}
