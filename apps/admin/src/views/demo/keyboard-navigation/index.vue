<script setup lang="ts">
import type { HerbalPrescriptionData } from '#/components/clinic/tcm-prescription-table/src/typing';

import { ref } from 'vue';

import { Page } from '@vben/common-ui';
import { buildShortUUID } from '@vben/utils';

import { TCMPrescriptionTable } from '#/components/clinic/tcm-prescription-table';

const prescriptionData = ref<HerbalPrescriptionData>({
  tableData: [
    {
      id: buildShortUUID(),
      name: '测试药品1',
      code: 'TEST001',
      price: 10.5,
      unit: 'g',
      amount: 15,
      subtotal: 157.5,
    },
    {
      id: buildShortUUID(),
      name: '测试药品2',
      code: 'TEST002',
      price: 8,
      unit: 'g',
      amount: 20,
      subtotal: 160,
    },
  ],
  prescription: '测试医嘱',
  dosage: 7,
  frequency: 'tid',
  usage: '水煎',
  usageValue: 200,
  isDecoction: 1,
});

const handleChange = (data: HerbalPrescriptionData) => {};

const addTestRow = () => {
  prescriptionData.value.tableData.push({
    id: buildShortUUID(),
    name: '',
    code: '-',
    price: 0,
    unit: '',
    amount: 1,
    subtotal: 0,
  });
};

const clearData = () => {
  prescriptionData.value.tableData = [];
};

const tableRef = ref();

const focusFirstCell = () => {
  if (prescriptionData.value.tableData.length > 0) {
    const firstRow = prescriptionData.value.tableData[0];
    // 聚焦到第一行的药品名称单元格
    setTimeout(() => {
      if (tableRef.value) {
        tableRef.value.setActiveCell(firstRow, 'name');
      }
    }, 100);
  }
};
</script>

<template>
  <div class="p-4">
    <Page>
      <PageMain>
        <p class="mb-4 text-gray-600">
          使用说明：
          <br />• 点击单元格进入编辑状态 <br />• 左右键：切换本行可编辑单元格
          <br />• 上下键：切换行 <br />• 在最后一行按下键：自动新增一行
        </p>
        <TCMPrescriptionTable
          ref="tableRef"
          v-model="prescriptionData"
          max-height="400px"
          @change="handleChange"
        />

        <div class="mt-4">
          <button
            @click="addTestRow"
            class="rounded bg-blue-500 px-4 py-2 text-white hover:bg-blue-600"
          >
            添加测试行
          </button>
          <button
            @click="clearData"
            class="ml-2 rounded bg-red-500 px-4 py-2 text-white hover:bg-red-600"
          >
            清空数据
          </button>
          <button
            @click="focusFirstCell"
            class="ml-2 rounded bg-green-500 px-4 py-2 text-white hover:bg-green-600"
          >
            聚焦第一个单元格
          </button>
        </div>

        <div class="mt-4 rounded bg-gray-100 p-4">
          <h3 class="mb-2 font-bold">当前数据：</h3>
          <pre>{{ JSON.stringify(prescriptionData, null, 2) }}</pre>
        </div>
      </PageMain>
    </Page>
  </div>
</template>
