<script setup lang="ts">
import type { VbenFormProps } from '@vben/common-ui';

import type { VxeGridProps } from '#/adapter/vxe-table';
import type {
  MedicalRecordForm,
  MedicalRecordVO,
} from '#/api/clinic/medical/record/model';
import type { PrintType } from '#/constants/qili-common-enum';

import { Page, useVbenDrawer } from '@vben/common-ui';

import { Modal, Space } from 'ant-design-vue';

import { useVbenVxeGrid, vxeCheckboxChecked } from '#/adapter/vxe-table';
import {
  medicalRecordExport,
  medicalRecordList,
  medicalRecordRemove,
} from '#/api/clinic/medical/record';
import { PrescriptionPrintMenu } from '#/components/clinic/prescription-print-menu';
import { commonDownloadExcel } from '#/utils/file/download';

import { columns, querySchema } from './data';
import medicalRecordDrawer from './medicalRecord-drawer.vue';

const formOptions: VbenFormProps = {
  commonConfig: {
    labelWidth: 80,
    componentProps: {
      allowClear: true,
    },
  },
  schema: querySchema(),
  wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
  // 处理区间选择器RangePicker时间格式 将一个字段映射为两个字段 搜索/导出会用到
  // 不需要直接删除
  // fieldMappingTime: [
  //  [
  //    'createTime',
  //    ['params[beginTime]', 'params[endTime]'],
  //    ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59'],
  //  ],
  // ],
};

const gridOptions: VxeGridProps = {
  checkboxConfig: {
    // 高亮
    highlight: true,
    // 翻页时保留选中状态
    reserve: true,
    // 点击行选中
    // trigger: 'row',
  },
  // 需要使用i18n注意这里要改成getter形式 否则切换语言不会刷新
  // columns: columns(),
  columns,
  height: 'auto',
  keepSource: true,
  pagerConfig: {},
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues = {}) => {
        return await medicalRecordList({
          pageNum: page.currentPage,
          pageSize: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  rowConfig: {
    keyField: 'id',
  },
  // 表格全局唯一表示 保存列配置需要用到
  id: 'clinic-medicalRecord-index',
};

const [BasicTable, tableApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

const [MedicalRecordDrawer, drawerApi] = useVbenDrawer({
  connectedComponent: medicalRecordDrawer,
});

function handleAdd() {
  drawerApi.setData({});
  drawerApi.open();
}

async function handleEdit(row: Required<MedicalRecordForm>) {
  drawerApi.setData({ id: row.id });
  drawerApi.open();
}

async function handleDelete(row: Required<MedicalRecordForm>) {
  await medicalRecordRemove(row.id);
  await tableApi.query();
}

function handleMultiDelete() {
  const rows = tableApi.grid.getCheckboxRecords();
  const ids = rows.map((row: Required<MedicalRecordForm>) => row.id);
  Modal.confirm({
    title: '提示',
    okType: 'danger',
    content: `确认删除选中的${ids.length}条记录吗？`,
    onOk: async () => {
      await medicalRecordRemove(ids);
      await tableApi.query();
    },
  });
}

function handleDownloadExcel() {
  commonDownloadExcel(
    medicalRecordExport,
    '病历/就诊记录数据',
    tableApi.formApi.form.values,
    {
      fieldMappingTime: formOptions.fieldMappingTime,
    },
  );
}

function buildPrintData(printType: PrintType, record: MedicalRecordVO) {
  return {
    printType,
    record,
  };
}
</script>

<template>
  <Page :auto-content-height="true">
    <BasicTable table-title="就诊记录列表">
      <template #toolbar-tools>
        <Space>
          <a-button
            v-access:code="['clinic:medicalRecord:export']"
            @click="handleDownloadExcel"
          >
            {{ $t('pages.common.export') }}
          </a-button>
          <a-button
            :disabled="!vxeCheckboxChecked(tableApi)"
            danger
            type="primary"
            v-access:code="['clinic:medicalRecord:remove']"
            @click="handleMultiDelete"
          >
            {{ $t('pages.common.delete') }}
          </a-button>
          <a-button
            type="primary"
            v-access:code="['clinic:medicalRecord:add']"
            @click="handleAdd"
          >
            {{ $t('pages.common.add') }}
          </a-button>
        </Space>
      </template>
      <template #action="{ row }">
        <Space>
          <ghost-button
            v-access:code="['clinic:medicalRecord:edit']"
            @click.stop="handleEdit(row)"
          >
            {{ $t('pages.common.info') }}
          </ghost-button>
          <PrescriptionPrintMenu :record="row" :print-data="buildPrintData" />
          <!-- <ghost-button
            v-access:code="['clinic:medicalRecord:edit']"
            @click.stop="handleEdit(row)"
          >
            {{ $t('pages.common.print') }}
          </ghost-button> -->
        </Space>
      </template>
    </BasicTable>
    <MedicalRecordDrawer @reload="tableApi.query()" />
  </Page>
</template>
