import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

import { QiliDictEnum } from '#/constants/qili-dict-enum';
import { getDictOptions } from '#/utils/dict';
import { renderDict } from '#/utils/render';

export const querySchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    fieldName: 'visitNo',
    label: '诊号',
  },
  {
    component: 'Input',
    fieldName: 'patientName',
    label: '患者姓名',
  },
  {
    component: 'Select',
    componentProps: {
      // 可选从DictEnum中获取 DictEnum.QILI_MEDICAL_RECORD_STATUS 便于维护
      options: getDictOptions(QiliDictEnum.QILI_MEDICAL_RECORD_STATUS),
    },
    fieldName: 'status',
    label: '就诊状态',
  },
  {
    component: 'DatePicker',
    componentProps: {
      showTime: true,
      format: 'YYYY-MM-DD HH:mm:ss',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    },
    fieldName: 'visitTime',
    label: '就诊时间',
  },
];

// 需要使用i18n注意这里要改成getter形式 否则切换语言不会刷新
// export const columns: () => VxeGridProps['columns'] = () => [
export const columns: VxeGridProps['columns'] = [
  { type: 'checkbox', width: 60 },
  {
    title: '诊号',
    field: 'visitNo',
  },
  {
    title: '患者姓名',
    field: 'patientName',
  },
  {
    title: '医生姓名',
    field: 'doctorName',
  },
  {
    title: '诊断',
    field: 'diagnosisJson',
    slots: {
      default: ({ row }) => {
        return (
          JSON.parse(row.diagnosisJson)?.fieldList.find(
            (item) => item.fieldName === '诊断',
          )?.fieldValue ?? '-'
        );
      },
    },
  },
  {
    title: '就诊状态',
    field: 'status',
    slots: {
      default: ({ row }) => {
        // 可选从DictEnum中获取 DictEnum.QILI_MEDICAL_RECORD_STATUS 便于维护
        return renderDict(row.status, QiliDictEnum.QILI_MEDICAL_RECORD_STATUS);
      },
    },
  },
  {
    title: '就诊时间',
    field: 'visitTime',
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    title: '操作',
    width: 180,
  },
];
