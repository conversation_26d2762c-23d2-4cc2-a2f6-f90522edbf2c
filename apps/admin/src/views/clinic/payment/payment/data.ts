import type { FormSchemaGetter } from '#/adapter/form';
import type { VxeGridProps } from '#/adapter/vxe-table';

import { QiliDictEnum } from '#/constants/qili-dict-enum';
import { renderDict, renderWithPrefix } from '#/utils/render';

export const querySchema: FormSchemaGetter = () => [
  {
    component: 'Input',
    fieldName: 'paymentNo',
    label: '支付单号',
  },
  {
    component: 'Input',
    fieldName: 'patientId',
    label: '患者ID',
  },
  {
    component: 'Select',
    componentProps: {},
    fieldName: 'orderType',
    label: '业务类型',
  },
  {
    component: 'Input',
    fieldName: 'paymentMethod',
    label: '支付方式',
  },
  {
    component: 'DatePicker',
    componentProps: {
      showTime: true,
      format: 'YYYY-MM-DD HH:mm:ss',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    },
    fieldName: 'paymentTime',
    label: '支付时间',
  },
];

// 需要使用i18n注意这里要改成getter形式 否则切换语言不会刷新
// export const columns: () => VxeGridProps['columns'] = () => [
export const columns: VxeGridProps['columns'] = [
  { type: 'checkbox', width: 60 },
  {
    title: '支付单号',
    field: 'paymentNo',
  },
  {
    title: '患者ID',
    field: 'patientId',
  },
  {
    title: '业务类型',
    field: 'orderType',
    slots: {
      default: ({ row }) =>
        renderDict(row.orderType, QiliDictEnum.QILI_PAYMENT_ORDER_TYPE),
    },
  },
  {
    title: '应付总金额',
    field: 'totalAmount',
    slots: {
      default: ({ row }) =>
        renderWithPrefix((row.totalAmount / 100).toFixed(2), '￥'),
    },
  },
  {
    title: '实付金额',
    field: 'paidAmount',
    slots: {
      default: ({ row }) =>
        renderWithPrefix((row.paidAmount / 100).toFixed(2), '￥'),
    },
  },
  {
    title: '支付状态',
    field: 'status',
    slots: {
      default: ({ row }) =>
        renderDict(row.status, QiliDictEnum.QILI_PAYMENT_STATUS),
    },
  },
  {
    title: '支付方式',
    field: 'paymentMethod',
    slots: {
      default: ({ row }) =>
        renderDict(row.paymentMethod, QiliDictEnum.QILI_PAYMENT_METHOD),
    },
  },
  {
    title: '支付时间',
    field: 'paymentTime',
  },
  {
    field: 'action',
    fixed: 'right',
    slots: { default: 'action' },
    title: '操作',
    width: 180,
  },
];
