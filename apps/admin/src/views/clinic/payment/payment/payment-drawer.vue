<!--
使用antd原生Form生成 详细用法参考ant-design-vue Form组件文档
vscode默认配置文件会自动格式化/移除未使用依赖
-->
<script setup lang="ts">
import type { RuleObject } from 'ant-design-vue/es/form';

import type { PaymentForm } from '#/api/clinic/payment/payment/model';

import { computed, ref } from 'vue';

import { useVbenDrawer } from '@vben/common-ui';
import { $t } from '@vben/locales';
import { cloneDeep } from '@vben/utils';

import { Descriptions, DescriptionsItem, Form } from 'ant-design-vue';
import { pick } from 'lodash-es';

import {
  paymentAdd,
  paymentInfo,
  paymentUpdate,
} from '#/api/clinic/payment/payment';
import { QiliDictEnum } from '#/constants/qili-dict-enum';
import { priceToYuan } from '#/utils/cis-common';
import { getDictLabel } from '#/utils/dict';
import { useBeforeCloseDiff } from '#/utils/popup';

const emit = defineEmits<{ reload: [] }>();

const isUpdate = ref(false);
const title = computed(() => {
  return isUpdate.value ? $t('pages.common.edit') : $t('pages.common.add');
});

/**
 * 定义默认值 用于reset
 */
const defaultValues: Partial<PaymentForm> = {
  id: undefined,
  paymentNo: undefined,
  patientId: undefined,
  orderType: undefined,
  totalAmount: undefined,
  paidAmount: undefined,
  status: undefined,
  paymentMethod: undefined,
  paymentTime: undefined,
  remark: undefined,
};

/**
 * 表单数据ref
 */
const formData = ref(defaultValues);

type AntdFormRules<T> = Partial<Record<keyof T, RuleObject[]>> & {
  [key: string]: RuleObject[];
};
/**
 * 表单校验规则
 */
const formRules = ref<AntdFormRules<PaymentForm>>({
  paymentMethod: [{ required: true, message: '支付方式不能为空' }],
  paymentTime: [{ required: true, message: '支付时间不能为空' }],
  remark: [{ required: true, message: '备注不能为空' }],
});

/**
 * useForm解构出表单方法
 */
const { validate, validateInfos, resetFields } = Form.useForm(
  formData,
  formRules,
);

function customFormValueGetter() {
  return JSON.stringify(formData.value);
}

const { onBeforeClose, markInitialized, resetInitialized } = useBeforeCloseDiff(
  {
    initializedGetter: customFormValueGetter,
    currentGetter: customFormValueGetter,
  },
);

const [BasicDrawer, drawerApi] = useVbenDrawer({
  class: 'w-[50%] min-w-[500px]',
  fullscreenButton: false,
  onBeforeClose,
  onClosed: handleClosed,
  onConfirm: handleConfirm,
  onOpenChange: async (isOpen) => {
    if (!isOpen) {
      return null;
    }
    drawerApi.drawerLoading(true);

    const { id } = drawerApi.getData() as { id?: number | string };
    isUpdate.value = !!id;

    if (isUpdate.value && id) {
      const record = await paymentInfo(id);
      // 只赋值存在的字段
      const filterRecord = pick(record, Object.keys(defaultValues));
      formData.value = filterRecord;
    }
    await markInitialized();

    drawerApi.drawerLoading(false);
  },
});

async function handleConfirm() {
  try {
    drawerApi.lock(true);
    await validate();
    // 可能会做数据处理 使用cloneDeep深拷贝
    const data = cloneDeep(formData.value);
    await (isUpdate.value ? paymentUpdate(data) : paymentAdd(data));
    resetInitialized();
    emit('reload');
    drawerApi.close();
  } catch (error) {
    console.error(error);
  } finally {
    drawerApi.lock(false);
  }
}

async function handleClosed() {
  formData.value = defaultValues;
  resetFields();
  resetInitialized();
}
</script>

<template>
  <BasicDrawer :title="title">
    <Descriptions :column="2" bordered>
      <DescriptionsItem label="支付单号">
        {{ formData.paymentNo || '-' }}
      </DescriptionsItem>
      <DescriptionsItem label="患者ID">
        {{ formData.patientId || '-' }}
      </DescriptionsItem>
      <DescriptionsItem :span="2" label="业务类型">
        {{
          getDictLabel(
            QiliDictEnum.QILI_PAYMENT_ORDER_TYPE,
            formData.orderType,
          ) || '-'
        }}
      </DescriptionsItem>
      <DescriptionsItem label="应付总金额">
        ￥{{ priceToYuan(formData.totalAmount ?? 0) }}
      </DescriptionsItem>
      <DescriptionsItem label="实付金额">
        ￥{{ priceToYuan(formData.paidAmount ?? 0) }}
      </DescriptionsItem>
      <DescriptionsItem label="支付状态">
        {{
          getDictLabel(QiliDictEnum.QILI_PAYMENT_STATUS, formData.status) || '-'
        }}
      </DescriptionsItem>
      <DescriptionsItem label="支付方式">
        {{
          getDictLabel(
            QiliDictEnum.QILI_PAYMENT_METHOD,
            formData.paymentMethod,
          ) || '-'
        }}
      </DescriptionsItem>
      <DescriptionsItem :span="2" label="支付时间">
        {{ formData.paymentTime || '-' }}
      </DescriptionsItem>
      <DescriptionsItem label="备注" :span="2">
        {{ formData.remark || '-' }}
      </DescriptionsItem>
    </Descriptions>
  </BasicDrawer>
</template>
