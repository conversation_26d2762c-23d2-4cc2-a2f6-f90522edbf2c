import type { MedicinePrescriptionData } from '#/components/clinic/medicine-prescription-table';
import type { HerbalPrescriptionData } from '#/components/clinic/tcm-prescription-table';
import type { TreatmentProjectData } from '#/components/clinic/treatment-project-table';

import { computed, ref } from 'vue';

/**
 * 统一管理门诊接诊页的处方/项目表单状态与常用计算
 * - 提供四类数据的 v-model 状态
 * - 提供变更回调（便于父组件监听 @change 并可扩展自动保存逻辑）
 * - 提供打印相关判定与总费用计算
 */
export function usePrescription() {
  // 中草药处方
  const herbalPrescriptionFormData = ref<HerbalPrescriptionData>({
    tableData: [],
    prescription: '',
    dosage: 1,
    frequency: 'tid',
    usage: '',
    usageValue: 0,
    isDecoction: 0,
  });

  // 中成药处方
  const chineseMedicinePrescriptionFormData = ref<MedicinePrescriptionData>({
    tableData: [],
    prescription: '',
  });

  // 西药处方
  const westernMedicinePrescriptionFormData = ref<MedicinePrescriptionData>({
    tableData: [],
    prescription: '',
  });

  // 诊疗项目
  const treatmentProjectFormData = ref<TreatmentProjectData>({
    tableData: [],
    prescription: '',
  });

  // 变更事件回调（可在父组件中绑定 @change 触发自动保存）
  const handleHerbalPrescriptionChange = (data: HerbalPrescriptionData) => {
    herbalPrescriptionFormData.value = { ...data };
  };

  const handleChineseMedicinePrescriptionChange = (
    data: MedicinePrescriptionData,
  ) => {
    chineseMedicinePrescriptionFormData.value = { ...data };
  };

  const handleWesternMedicinePrescriptionChange = (
    data: MedicinePrescriptionData,
  ) => {
    westernMedicinePrescriptionFormData.value = { ...data };
  };

  const handleTreatmentProjectChange = (data: TreatmentProjectData) => {
    treatmentProjectFormData.value = { ...data };
  };

  // 打印可用性
  const isTcmPrescriptionPrint = computed(() => {
    return herbalPrescriptionFormData.value.tableData.length > 0;
  });
  const isZcyPrescriptionPrint = computed(() => {
    return chineseMedicinePrescriptionFormData.value.tableData.length > 0;
  });
  const isXyPrescriptionPrint = computed(() => {
    return westernMedicinePrescriptionFormData.value.tableData.length > 0;
  });

  // 中草药费用
  const herbalPrescriptionAmount = computed(() => {
    if (
      !herbalPrescriptionFormData.value.tableData ||
      herbalPrescriptionFormData.value.tableData.length === 0
    ) {
      return 0;
    }
    let total = 0;
    for (const item of herbalPrescriptionFormData.value.tableData) {
      const price = Number(item.price) || 0;
      const amount = Number(item.amount) || 0;
      total += price * amount;
    }
    const dosage = Number(herbalPrescriptionFormData.value.dosage) || 1;
    return Number((total * dosage).toFixed(2)) || 0;
  });

  // 中成药费用
  const chineseMedicineAmount = computed(() => {
    if (
      !chineseMedicinePrescriptionFormData.value.tableData ||
      chineseMedicinePrescriptionFormData.value.tableData.length === 0
    ) {
      return 0;
    }
    let total = 0;
    for (const item of chineseMedicinePrescriptionFormData.value.tableData) {
      const price = Number(item.price) || 0;
      const totalAmount = Number(item.totalAmount) || 0;
      total += price * totalAmount;
    }
    return Number(total.toFixed(2)) || 0;
  });

  // 西药费用
  const westernMedicineAmount = computed(() => {
    if (
      !westernMedicinePrescriptionFormData.value.tableData ||
      westernMedicinePrescriptionFormData.value.tableData.length === 0
    ) {
      return 0;
    }
    let total = 0;
    for (const item of westernMedicinePrescriptionFormData.value.tableData) {
      const price = Number(item.price) || 0;
      const totalAmount = Number(item.totalAmount) || 0;
      total += price * totalAmount;
    }
    return Number(total.toFixed(2)) || 0;
  });

  // 诊疗项目费用
  const treatmentProjectAmount = computed(() => {
    if (
      !treatmentProjectFormData.value.tableData ||
      treatmentProjectFormData.value.tableData.length === 0
    ) {
      return 0;
    }
    let total = 0;
    for (const item of treatmentProjectFormData.value.tableData) {
      const price = Number(item.price) || 0;
      const amount = Number(item.amount) || 0;
      total += price * amount;
    }
    return Number(total.toFixed(2)) || 0;
  });

  // 统一总费用
  const paymentTotalAmount = computed(() => {
    return Number(
      (
        herbalPrescriptionAmount.value +
        chineseMedicineAmount.value +
        westernMedicineAmount.value +
        treatmentProjectAmount.value
      ).toFixed(2),
    );
  });

  return {
    // state
    herbalPrescriptionFormData,
    chineseMedicinePrescriptionFormData,
    westernMedicinePrescriptionFormData,
    treatmentProjectFormData,

    // events
    handleHerbalPrescriptionChange,
    handleChineseMedicinePrescriptionChange,
    handleWesternMedicinePrescriptionChange,
    handleTreatmentProjectChange,

    // computed
    isTcmPrescriptionPrint,
    isZcyPrescriptionPrint,
    isXyPrescriptionPrint,
    paymentTotalAmount,
    herbalPrescriptionAmount,
    chineseMedicineAmount,
    westernMedicineAmount,
    treatmentProjectAmount,
  };
}
