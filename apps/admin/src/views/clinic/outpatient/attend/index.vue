<script setup lang="ts">
import type {
  MedicalRecordForm,
  MedicalRecordVO,
} from '#/api/clinic/medical/record/model';
import type { MedicalTemplateVO } from '#/api/clinic/medical/template/model';
import type { PatientVO } from '#/api/clinic/patient/model';
import type { PaymentForm } from '#/api/clinic/payment/payment/model';
import type { ID } from '#/api/common';
import type { MedicinePrescriptionData } from '#/components/clinic/medicine-prescription-table';
import type { HerbalPrescriptionData } from '#/components/clinic/tcm-prescription-table';
import type { TreatmentProjectData } from '#/components/clinic/treatment-project-table';
import type { ComponentRefs, FormRefs } from '#/utils/clinic';

import { computed, onMounted, ref, unref } from 'vue';

import { Page } from '@vben/common-ui';

import { Avatar, Button, Card, Empty, message, Modal } from 'ant-design-vue';
import { cloneDeep } from 'lodash-es';

import {
  medicalRecordAdd,
  medicalRecordCancel,
  medicalRecordComplete,
  medicalRecordInfo,
} from '#/api/clinic/medical/record';
import { medicalDefaultTemplateInfo } from '#/api/clinic/medical/template';
import { AutofillConfirmModal } from '#/components/clinic/autofill-confirm-modal';
import { MedicalCard } from '#/components/clinic/medical-card';
import { MedicalRecordHistory } from '#/components/clinic/medical-record-history';
import { MedicinePrescriptionCard } from '#/components/clinic/medicine-prescription-card';
import { MedicineCategory } from '#/components/clinic/medicine-prescription-table';
import { PatientList } from '#/components/clinic/patient-list';
import { PaymentModal } from '#/components/clinic/payment-modal';
import { PrescriptionPrintMenu } from '#/components/clinic/prescription-print-menu';
import { TCMPrescriptionCard } from '#/components/clinic/tcm-prescription-card';
import { TemplateSelector } from '#/components/clinic/template-selector';
import { TreatmentProjectCard } from '#/components/clinic/treatment-project-card';
import { MedicalRecordStatus, PrintType } from '#/constants/qili-common-enum';
import { QiliDictEnum } from '#/constants/qili-dict-enum';
import { isTrue } from '#/utils/cis-common';
import {
  AutofillType,
  FormFillService,
  HistoryDataParser,
} from '#/utils/clinic';
import { getDictLabel } from '#/utils/dict';

import { usePrescription } from './usePrescription';

// 统一的处方/项目状态与计算
const {
  herbalPrescriptionFormData,
  chineseMedicinePrescriptionFormData,
  westernMedicinePrescriptionFormData,
  treatmentProjectFormData,
  isTcmPrescriptionPrint,
  isZcyPrescriptionPrint,
  isXyPrescriptionPrint,
  herbalPrescriptionAmount,
  chineseMedicineAmount,
  westernMedicineAmount,
  treatmentProjectAmount,
} = usePrescription();
const currentPatient = ref<null | PatientVO>(null);

const tcmPrescriptionCard = ref();
const chineseMedicinePrescriptionCard = ref();
const westernMedicinePrescriptionCard = ref();
const treatmentProjectCard = ref();
const medicalRecordCard = ref();
const patientListRef = ref();
const paymentModalRef = ref();
const medicalRecordHistoryRef = ref();

// 填充功能相关状态
const showAutofillConfirmModal = ref(false);
const currentAutofillType = ref<AutofillType>(AutofillType.ALL);
const currentAutofillRecord = ref<MedicalRecordVO | null>(null);
const hasExistingDataForAutofill = ref(false);
const autofillLoading = ref(false);

// 为了在Vue DevTools中查看，添加计算属性
const tcmPrescription = computed(() => herbalPrescriptionFormData.value);
const tcmpPrescription = computed(
  () => chineseMedicinePrescriptionFormData.value,
);
const _wmPrescription = computed(
  () => westernMedicinePrescriptionFormData.value,
);
const _treatmentItems = computed(() => treatmentProjectFormData.value);
const diagnosisData = computed(() => medicalTemplate.value);

// 支付弹窗相关状态
const showPaymentModal = ref(false);
const paymentPreview = ref(false);
// 费用缓存，用于在数据清空后仍能显示费用信息
const costCache = ref<null | {
  chineseMedicineAmount: number;
  herbalPrescriptionAmount: number;
  treatmentProjectAmount: number;
  westernMedicineAmount: number;
}>(null);

// 检查是否有有效的费用缓存
const hasValidCostCache = computed(() => {
  if (!costCache.value) return false;
  return (
    costCache.value.herbalPrescriptionAmount > 0 ||
    costCache.value.chineseMedicineAmount > 0 ||
    costCache.value.westernMedicineAmount > 0 ||
    costCache.value.treatmentProjectAmount > 0
  );
});

const paymentItems = computed(() => {
  // 获取费用数据，优先使用缓存
  const herbalAmount =
    costCache.value?.herbalPrescriptionAmount ??
    unref(herbalPrescriptionAmount);
  const chineseAmount =
    costCache.value?.chineseMedicineAmount ?? unref(chineseMedicineAmount);
  const westernAmount =
    costCache.value?.westernMedicineAmount ?? unref(westernMedicineAmount);
  const treatmentAmount =
    costCache.value?.treatmentProjectAmount ?? unref(treatmentProjectAmount);

  // 确保所有费用都是有效的数值
  const items = [
    {
      name: '中药处方',
      price: Number.isFinite(herbalAmount) ? herbalAmount : 0,
    },
    {
      name: '中成药处方',
      price: Number.isFinite(chineseAmount) ? chineseAmount : 0,
    },
    {
      name: '西药处方',
      price: Number.isFinite(westernAmount) ? westernAmount : 0,
    },
    {
      name: '诊疗项目',
      price: Number.isFinite(treatmentAmount) ? treatmentAmount : 0,
    },
  ];

  // 调试信息
  console.log('费用计算详情:', {
    herbalAmount,
    chineseAmount,
    westernAmount,
    treatmentAmount,
    items,
    total: items.reduce((sum, item) => sum + item.price, 0),
  });

  return items;
});

const getCurrentPatientAvatarBg = computed(() => {
  if (!currentPatient.value) return 'man-avatar';
  return currentPatient.value?.gender === '1' ? 'man-avatar' : 'woman-avatar';
});

// 切换患者
const handlePatientSelect = (patient: PatientVO) => {
  if (currentPatient.value?.id === patient.id) {
    currentPatient.value = null;
    currentMedicalRecord.value = null;
    medicalRecordHistoryList.value = [];
    return;
  }
  currentPatient.value = patient;
  // currentMedicalRecord.value = null;
  currentMedicalRecord.value = patient.medicalRecord;
  medicalRecordHistoryList.value = [];
};

// 就诊历史记录列表（由 MedicalRecordHistory 组件管理）
const medicalRecordHistoryList = ref<MedicalRecordVO[]>([]);

// 处理就诊历史记录加载完成
const handleRecordsLoaded = (
  records: MedicalRecordVO[],
  currentRecord: MedicalRecordVO | null,
) => {
  medicalRecordHistoryList.value = records;
  if (currentRecord) {
    currentMedicalRecord.value = currentRecord;
  }
};

// 中草药处方数据变化回调
const handleHerbalPrescriptionChange = (data: HerbalPrescriptionData) => {
  // 可以在这里添加自动保存逻辑
  autoSaveHerbalPrescription(data);
};

// 中成药处方数据变化回调
const handleChineseMedicinePrescriptionChange = (
  data: MedicinePrescriptionData,
) => {
  // 可以在这里添加自动保存逻辑
  autoSaveChineseMedicinePrescription(data);
};

// 西药处方数据变化回调
const handleWesternMedicinePrescriptionChange = (
  data: MedicinePrescriptionData,
) => {
  // 可以在这里添加自动保存逻辑
  autoSaveWesternMedicinePrescription(data);
};

// 诊疗项目数据变化回调
const handleTreatmentProjectChange = (data: TreatmentProjectData) => {
  // 可以在这里添加自动保存逻辑
  autoSaveTreatmentProject(data);
};

// 自动保存中草药处方
const autoSaveHerbalPrescription = async (_data: HerbalPrescriptionData) => {
  // 可以在这里实现自动保存逻辑
  // await saveHerbalPrescriptionData(_data);
};

// 自动保存中成药处方
const autoSaveChineseMedicinePrescription = async (
  _data: MedicinePrescriptionData,
) => {
  try {
    // 这里调用API保存数据，category: ZCY
    // await saveMedicinePrescriptionData(currentPatient.value?.id, _data, 'ZCY');
    console.log('自动保存中成药处方成功');
  } catch (error) {
    console.error('自动保存中成药处方失败:', error);
  }
};

// 自动保存西药处方
const autoSaveWesternMedicinePrescription = async (
  _data: MedicinePrescriptionData,
) => {
  try {
    // 这里调用API保存数据，category: XY
    // await saveMedicinePrescriptionData(currentPatient.value?.id, _data, 'XY');
    console.log('自动保存西药处方成功');
  } catch (error) {
    console.error('自动保存西药处方失败:', error);
  }
};

// 自动保存诊疗项目
const autoSaveTreatmentProject = async (_data: TreatmentProjectData) => {
  try {
    // 这里调用API保存数据
    // await saveTreatmentProjectData(currentPatient.value?.id, _data);
    console.log('自动保存诊疗项目成功');
  } catch (error) {
    console.error('自动保存诊疗项目失败:', error);
  }
};

const medicalTemplate = ref<MedicalTemplateVO>({
  id: '',
  name: '',
  type: '',
  directory: '',
  description: '',
  fieldList: [],
});

// 初始化填充服务
const formRefs: FormRefs = {
  medicalTemplate,
  herbalPrescriptionFormData,
  chineseMedicinePrescriptionFormData,
  westernMedicinePrescriptionFormData,
  treatmentProjectFormData,
};

const componentRefs: ComponentRefs = {
  medicalRecordCard,
  tcmPrescriptionCard,
  chineseMedicinePrescriptionCard,
  westernMedicinePrescriptionCard,
  treatmentProjectCard,
};

// 填充确认回调
const handleAutofillConfirm = async (
  type: AutofillType,
  hasExistingData: boolean,
): Promise<boolean> => {
  // 简化逻辑：直接显示确认对话框，不等待Promise
  currentAutofillType.value = type;
  hasExistingDataForAutofill.value = hasExistingData;
  showAutofillConfirmModal.value = true;

  // 返回true表示用户已确认（实际确认在对话框关闭时处理）
  return true;
};

const fillService = new FormFillService(
  formRefs,
  componentRefs,
  handleAutofillConfirm,
);

// 获取默认病历模板
const getDefalutMedicalTemplate = async () => {
  const res = await medicalDefaultTemplateInfo();
  medicalTemplate.value = res;
};

onMounted(() => {
  getDefalutMedicalTemplate();
});

// 构建打印数据
const buildPrintData = (printType: PrintType) => {
  // 将当前中草药处方数据转换为打印格式
  const tcmPrescriptionData = herbalPrescriptionFormData?.value;
  const zcyPrescriptionData = chineseMedicinePrescriptionFormData?.value;
  const xyPrescriptionData = westernMedicinePrescriptionFormData?.value;
  return {
    printType,
    patientId: currentPatient.value?.id,
    medicalField: medicalTemplate.value,
    visitNo: currentMedicalRecord.value?.visitNo,
    visitTime: currentMedicalRecord.value?.visitTime,
    totalAmount: 0,
    tcmPrescriptionData,
    zcyPrescriptionData,
    xyPrescriptionData,
  };
};

// 模板选择相关
const showPrescriptionTemplateSelector = ref(false);

// 选择处方模板
const handlePrescriptionTemplateSelect = (template: any) => {
  // 这里可以根据实际的处方模板数据结构来处理
  showPrescriptionTemplateSelector.value = false;
  message.success(`成功应用处方模板：${template.name}`);
};

// 当前就诊记录
const currentMedicalRecord = ref<MedicalRecordVO | null>(null);
// 开始接诊
const handleStartAttend = async () => {
  if (!currentPatient.value?.id) {
    message.error('请先选择患者');
    return;
  }
  const data: MedicalRecordForm = {
    patientId: currentPatient.value.id,
    patientName: currentPatient.value.name,
    status: MedicalRecordStatus.IN_PROGRESS,
  };
  const recordId = await medicalRecordAdd(data);
  if (!recordId) {
    return;
  }
  const res = await medicalRecordInfo(recordId);
  currentMedicalRecord.value = res;
  message.success('开始接诊');
};

// 处理从新建患者弹窗触发的开始接诊事件
const handleStartVisit = async (patient: any) => {
  // 设置当前患者
  currentPatient.value = patient;
  // 调用开始接诊逻辑
  await handleStartAttend();
  // 刷新患者列表
  patientListRef.value?.refresh?.();
};

// 取消接诊
const handleCancelAttend = async () => {
  // 弹窗提示是否取消接诊
  Modal.confirm({
    title: '取消接诊',
    content: '您确定取消接诊吗？取消后不会保存本次病历和处方数据',
    onOk: async () => {
      // 取消接诊
      await medicalRecordCancel(currentMedicalRecord.value?.id as ID);
      // 刷新患者列表
      patientListRef.value?.refresh?.();
      // 更新历史就诊记录
      medicalRecordHistoryRef.value?.refreshData?.();
    },
  });
};

const handleEndAttend = async () => {
  // 检查必填字段是否填写
  if (!validMedicalTemplate()) {
    return;
  }
  // 统一校验三张处方表
  const ok = await validateAllPrescriptionTables();
  if (!ok) {
    return;
  }

  // 在显示支付弹窗前先缓存费用数据
  costCache.value = {
    herbalPrescriptionAmount: unref(herbalPrescriptionAmount),
    chineseMedicineAmount: unref(chineseMedicineAmount),
    westernMedicineAmount: unref(westernMedicineAmount),
    treatmentProjectAmount: unref(treatmentProjectAmount),
  };

  console.log('完成接诊时缓存费用数据:', costCache.value);

  // 显示支付弹窗
  showPaymentModal.value = true;
  paymentPreview.value = false;
};

// 支付确认处理
const handlePaymentConfirm = async (_paymentData: any) => {
  // 病历数据
  const medicalData = cloneDeep(medicalTemplate.value);
  // 不需要fieldList中的fieldDataList字段
  medicalData.fieldList = medicalData.fieldList.map((item) => {
    return {
      ...item,
      fieldDataList: undefined,
    };
  });
  const medicalRecordStr = JSON.stringify(medicalData);

  // 获取处方数据
  const tcmPrescriptionData = tcmPrescriptionCard.value?.getData();
  const chineseMedicinePrescriptionData =
    chineseMedicinePrescriptionCard.value?.getData();
  const westernMedicinePrescriptionData =
    westernMedicinePrescriptionCard.value?.getData();
  const treatmentProjectData = treatmentProjectCard.value?.getData();

  // 序列化处方数据
  const tcmPrescriptionDataStr = JSON.stringify(tcmPrescriptionData);
  const chineseMedicinePrescriptionDataStr = JSON.stringify(
    chineseMedicinePrescriptionData,
  );
  const westernMedicinePrescriptionDataStr = JSON.stringify(
    westernMedicinePrescriptionData,
  );

  // 诊疗项目字典
  if (treatmentProjectData) {
    treatmentProjectData.dictMapping =
      treatmentProjectCard.value?.getDictMapping();
  }
  const treatmentProjectDataStr = JSON.stringify(treatmentProjectData);

  // 支付数据
  const payment = paymentModalRef.value?.getPaymentData() as PaymentForm;
  payment.paymentDetailList = [];

  // 中药处方
  if (tcmPrescriptionData?.tableData) {
    tcmPrescriptionData.tableData.forEach((item) => {
      payment.paymentDetailList?.push({
        drugId: item.id,
        itemName: item.name,
        itemCode: 'CY',
        quantity: item.amount,
        price: item.price,
        unit: item.unit,
        amount: item.subtotal,
      });
    });
  }

  // 中成药处方
  if (chineseMedicinePrescriptionData?.tableData) {
    chineseMedicinePrescriptionData.tableData.forEach((item) => {
      payment.paymentDetailList?.push({
        drugId: item.id,
        itemName: item.name,
        itemCode: 'ZCY',
        quantity: item.totalAmount,
        price: item.price,
        unit: item.unit,
        amount: item.price * item.totalAmount,
      });
    });
  }

  // 西药处方
  if (westernMedicinePrescriptionData?.tableData) {
    westernMedicinePrescriptionData.tableData.forEach((item) => {
      payment.paymentDetailList?.push({
        drugId: item.id,
        itemName: item.name,
        itemCode: 'XY',
        quantity: item.totalAmount,
        price: item.price,
        unit: item.unit,
        amount: item.price * item.totalAmount,
      });
    });
  }

  // 诊疗项目
  if (treatmentProjectData?.tableData) {
    treatmentProjectData.tableData.forEach((item) => {
      payment.paymentDetailList?.push({
        drugId: item.id,
        itemName: item.name,
        itemCode: 'ZL',
        quantity: item.durationMinutes,
        price: item.price,
        unit: item.unit,
        amount: item.totalPrice,
      });
    });
  }

  const data = {
    id: currentMedicalRecord.value?.id,
    diagnosisJson: medicalRecordStr,
    tcmPrescriptionJson: tcmPrescriptionDataStr,
    tcmpPrescriptionJson: chineseMedicinePrescriptionDataStr,
    wmPrescriptionJson: westernMedicinePrescriptionDataStr,
    treatmentItemJson: treatmentProjectDataStr,
    payment,
  } as MedicalRecordForm;
  // 保存病历数据
  await medicalRecordComplete(data);

  // 刷新患者列表
  patientListRef.value?.refresh?.();
  const patientId = currentPatient.value?.id as ID;
  currentPatient.value = null;
  // 清空处方数据
  medicalTemplate.value = {
    id: '',
    name: '',
    type: '',
    directory: '',
    description: '',
    fieldList: [],
  };
  tcmPrescriptionCard.value?.clearData();
  chineseMedicinePrescriptionCard.value?.clearData();
  westernMedicinePrescriptionCard.value?.clearData();
  treatmentProjectCard.value?.clearData();

  // 清除费用缓存
  costCache.value = null;

  // 更新历史就诊记录
  medicalRecordHistoryRef.value?.refreshData?.();
};

// 支付取消处理
const handlePaymentCancel = () => {
  showPaymentModal.value = false;
};
// 校验病历模板
const validMedicalTemplate = () => {
  const requiredFields = medicalTemplate.value.fieldList.filter(
    (item) => isTrue(item.required) && !item.fieldValue,
  );
  if (requiredFields.length > 0) {
    message.error(
      `请填写病历必填字段: ${requiredFields.map((item) => item.fieldName).join(', ')}`,
    );
    return false;
  }
  return true;
};
// 费用预览
const handleCostPreview = () => {
  // 检查是否有处方数据
  const hasPrescriptionData =
    herbalPrescriptionFormData.value.tableData?.length > 0 ||
    chineseMedicinePrescriptionFormData.value.tableData?.length > 0 ||
    westernMedicinePrescriptionFormData.value.tableData?.length > 0 ||
    treatmentProjectFormData.value.tableData?.length > 0;

  console.log('费用预览 - 当前处方数据状态:', {
    hasPrescriptionData,
    herbalData: herbalPrescriptionFormData.value.tableData,
    chineseData: chineseMedicinePrescriptionFormData.value.tableData,
    westernData: westernMedicinePrescriptionFormData.value.tableData,
    treatmentData: treatmentProjectFormData.value.tableData,
  });

  if (!hasPrescriptionData) {
    // 如果没有当前数据，检查是否有缓存数据
    if (hasValidCostCache.value) {
      message.info('显示上次的费用信息（数据已清空）');
    } else {
      message.warning('请先添加处方或诊疗项目后再查看费用');
      return;
    }
  }

  // 缓存当前费用数据
  costCache.value = {
    herbalPrescriptionAmount: unref(herbalPrescriptionAmount),
    chineseMedicineAmount: unref(chineseMedicineAmount),
    westernMedicineAmount: unref(westernMedicineAmount),
    treatmentProjectAmount: unref(treatmentProjectAmount),
  };

  console.log('费用预览 - 缓存费用数据:', costCache.value);

  showPaymentModal.value = true;
  paymentPreview.value = true;
};

// 统一触发表格校验（使用子组件暴露的 validate）
const validateAllPrescriptionTables = async (): Promise<boolean> => {
  try {
    const herbalOk = (await tcmPrescriptionCard.value?.validate?.()) !== false;
    if (!herbalOk) {
      return false;
    }
    const zcyOk =
      (await chineseMedicinePrescriptionCard.value?.validate?.()) !== false;
    if (!zcyOk) {
      return false;
    }
    const xyOk =
      (await westernMedicinePrescriptionCard.value?.validate?.()) !== false;
    if (!xyOk) {
      return false;
    }
    const treatmentProjectOk =
      (await treatmentProjectCard.value?.validate?.()) !== false;
    if (!treatmentProjectOk) {
      return false;
    }
    return true;
  } catch {
    message.error('请修正处方表中的校验错误');
    return false;
  }
};

// 填充功能处理函数
const handleAutofillAll = async (record: MedicalRecordVO) => {
  // 检查是否有现有数据
  const hasExistingData = checkExistingData();

  if (hasExistingData) {
    // 有现有数据时，显示确认对话框
    currentAutofillRecord.value = record;
    hasExistingDataForAutofill.value = true;
    showAutofillConfirmModal.value = true;
  } else {
    // 没有现有数据时，直接填充
    await performAutofill(record);
  }
};

// 执行实际的填充操作
const performAutofill = async (record: MedicalRecordVO) => {
  try {
    const result = await fillService.fillAllForms(record);

    if (result.success) {
      if (result.filledForms.length > 0) {
        message.success(`成功填充：${result.filledForms.join('、')}`);
      }
      if (result.skippedForms.length > 0) {
        message.warning(`跳过：${result.skippedForms.join('、')}`);
      }
    } else {
      message.error(`填充失败：${result.errors.join('、')}`);
    }
  } catch (error) {
    console.error('填充失败:', error);
    message.error('填充操作失败，请重试');
  }
};

// 检查是否有现有数据
const checkExistingData = (): boolean => {
  // 简单的检查逻辑
  return (
    medicalTemplate.value.fieldList?.some((field) => field.fieldValue) ||
    herbalPrescriptionFormData.value.tableData?.length > 0 ||
    chineseMedicinePrescriptionFormData.value.tableData?.length > 0 ||
    westernMedicinePrescriptionFormData.value.tableData?.length > 0 ||
    treatmentProjectFormData.value.tableData?.length > 0
  );
};

const handleAutofillDiagnosis = async (record: MedicalRecordVO) => {
  const diagnosisData = HistoryDataParser.parseDiagnosisData(
    record.diagnosisJson,
  );
  if (!diagnosisData) {
    message.error('病历诊断数据格式错误');
    return;
  }

  try {
    await fillService.fillDiagnosisForm(diagnosisData);
  } catch (error) {
    console.error('填充失败:', error);
    message.error('填充操作失败，请重试');
  }
};

const handleAutofillTcm = async (record: MedicalRecordVO) => {
  const tcmData = HistoryDataParser.parseTcmPrescriptionData(
    record.tcmPrescriptionJson,
  );
  if (!tcmData) {
    message.error('中药处方数据格式错误');
    return;
  }

  try {
    await fillService.fillTcmPrescriptionForm(tcmData);
  } catch (error) {
    console.error('填充失败:', error);
    message.error('填充操作失败，请重试');
  }
};

const handleAutofillChineseMedicine = async (record: MedicalRecordVO) => {
  const chineseMedicineData = HistoryDataParser.parseChineseMedicineData(
    record.tcmpPrescriptionJson,
  );
  if (!chineseMedicineData) {
    message.error('中成药处方数据格式错误');
    return;
  }

  try {
    await fillService.fillChineseMedicineForm(chineseMedicineData);
  } catch (error) {
    console.error('填充失败:', error);
    message.error('填充操作失败，请重试');
  }
};

const handleAutofillWesternMedicine = async (record: MedicalRecordVO) => {
  const westernMedicineData = HistoryDataParser.parseWesternMedicineData(
    record.wmPrescriptionJson,
  );
  if (!westernMedicineData) {
    message.error('西药处方数据格式错误');
    return;
  }

  try {
    await fillService.fillWesternMedicineForm(westernMedicineData);
  } catch (error) {
    console.error('填充失败:', error);
    message.error('填充操作失败，请重试');
  }
};

const handleAutofillTreatment = async (record: MedicalRecordVO) => {
  const treatmentData = HistoryDataParser.parseTreatmentData(
    record.treatmentItemJson,
  );
  if (!treatmentData) {
    message.error('诊疗项目数据格式错误');
    return;
  }

  try {
    await fillService.fillTreatmentForm(treatmentData);
  } catch (error) {
    console.error('填充失败:', error);
    message.error('填充操作失败，请重试');
  }
};

// 确认对话框处理
const handleConfirmModalConfirm = () => {
  showAutofillConfirmModal.value = false;
  // 执行填充操作
  if (currentAutofillRecord.value) {
    performAutofill(currentAutofillRecord.value);
  }
  // 清理状态
  currentAutofillRecord.value = null;
  hasExistingDataForAutofill.value = false;
};

const handleConfirmModalCancel = () => {
  showAutofillConfirmModal.value = false;
  // 清理状态
  currentAutofillRecord.value = null;
  hasExistingDataForAutofill.value = false;
};

// 清空所有表单数据
const handleClearAll = () => {
  Modal.confirm({
    title: '清空所有数据',
    content: '您确定要清空所有表单数据吗？此操作不可恢复',
    onOk: () => {
      // 清空病历模板数据
      medicalTemplate.value = {
        id: '',
        name: '',
        type: '',
        directory: '',
        description: '',
        fieldList: medicalTemplate.value.fieldList.map((field) => ({
          ...field,
          fieldValue: undefined,
        })),
      };

      // 清空处方数据
      herbalPrescriptionFormData.value = {
        tableData: [],
        prescription: '',
        dosage: 1,
        frequency: 'tid',
        usage: '',
        usageValue: 0,
        isDecoction: 0,
      };

      chineseMedicinePrescriptionFormData.value = {
        tableData: [],
        prescription: '',
      };

      westernMedicinePrescriptionFormData.value = {
        tableData: [],
        prescription: '',
      };

      treatmentProjectFormData.value = {
        tableData: [],
        prescription: '',
      };

      // 清空子组件数据
      tcmPrescriptionCard.value?.clearData?.();
      chineseMedicinePrescriptionCard.value?.clearData?.();
      westernMedicinePrescriptionCard.value?.clearData?.();
      treatmentProjectCard.value?.clearData?.();

      // 清除费用缓存
      costCache.value = null;

      message.success('所有表单数据已清空');
    },
  });
};
</script>

<template>
  <Page :auto-content-height="true">
    <div class="flex h-full w-full gap-[8px]">
      <Card size="small" class="w-[20%] overflow-y-auto">
        <PatientList
          ref="patientListRef"
          :selected-id="currentPatient?.id"
          @select="handlePatientSelect"
          @start-visit="handleStartVisit"
        />
      </Card>
      <!-- 中部内容 -->
      <div class="flex h-full w-[60%] flex-1 flex-col gap-2 overflow-y-auto">
        <Card size="small" class="sticky left-0 top-0 z-50">
          <div class="grid grid-cols-2 gap-2">
            <div class="flex items-center gap-1">
              <Avatar
                :size="48"
                :class="getCurrentPatientAvatarBg"
                class="flex items-center justify-center"
              >
                <span v-if="currentPatient?.name">
                  {{ currentPatient?.name?.charAt(0) }}
                </span>
                <i
                  v-else
                  class="icon-[mynaui--heart-user] flex text-center text-2xl"
                ></i>
              </Avatar>
              <div class="ml-4 flex flex-col gap-1 text-base">
                <div class="flex items-center gap-1 font-medium">
                  <label>姓名:</label>
                  <span>
                    {{ currentPatient?.name || '--' }}
                  </span>
                </div>
                <div class="flex items-center gap-1 text-xs text-gray-400">
                  <label>诊号:</label>
                  <span>
                    {{ currentMedicalRecord?.visitNo || '接诊自动生成' }}
                  </span>
                </div>
              </div>
            </div>
            <div class="mt-1 flex items-center justify-end gap-2">
              <!-- 就诊中状态：显示取消接诊和完成接诊按钮 -->
              <template
                v-if="
                  currentMedicalRecord?.id &&
                  currentMedicalRecord.status ===
                    MedicalRecordStatus.IN_PROGRESS
                "
              >
                <Button
                  type="primary"
                  danger
                  size="small"
                  class="flex items-center"
                  @click="handleCancelAttend"
                >
                  <i
                    class="icon-[material-symbols--cancel-outline-rounded]"
                  ></i>
                  取消
                </Button>
                <Button
                  type="primary"
                  size="small"
                  class="bg-success flex items-center text-white"
                  @click="handleEndAttend"
                >
                  <i class="icon-[lets-icons--check-fill]"></i>
                  完成
                </Button>
              </template>

              <!-- 非就诊中状态：显示开始接诊按钮 -->
              <template v-else>
                <Button
                  :disabled="!currentPatient?.id"
                  type="primary"
                  size="small"
                  class="flex items-center"
                  @click="handleStartAttend"
                >
                  <i class="icon-[ph--hand-heart-light]"></i>
                  接诊
                </Button>
              </template>
              <Button
                type="primary"
                size="small"
                class="flex items-center"
                @click="handleCostPreview"
              >
                <i class="icon-[ri--money-cny-circle-line]"></i>
                费用
              </Button>
              <Button
                type="primary"
                danger
                size="small"
                class="flex items-center"
                @click="handleClearAll"
              >
                <i class="icon-[grommet-icons--power-reset]"></i>
                清空
              </Button>
              <PrescriptionPrintMenu
                :is-tcm-prescription-print="isTcmPrescriptionPrint"
                :is-zcy-prescription-print="isZcyPrescriptionPrint"
                :is-xy-prescription-print="isXyPrescriptionPrint"
                :current-patient="currentPatient"
                :print-data="buildPrintData"
              />
            </div>
          </div>
          <div class="ml-2 mt-2 flex items-center justify-start gap-2">
            <div>
              <label class="font-medium">性别：</label>
              <span class="text-gray-400">
                {{
                  getDictLabel(
                    QiliDictEnum.QILI_PATIENT_GENDER,
                    currentPatient?.gender || '',
                  ) || '--'
                }}
              </span>
            </div>
            <div>
              <label class="font-medium">年龄：</label>
              <span class="text-gray-400">
                {{ currentPatient?.ageYear || '--' }}岁
              </span>
            </div>
            <div>
              <label class="font-medium">电话：</label>
              <span class="text-gray-400">
                {{ currentPatient?.phone || '--' }}
              </span>
            </div>
            <div>
              <label class="font-medium">上次就诊：</label>
              <span class="text-gray-400">
                {{ currentPatient?.medicalRecord?.visitTime || '--' }}
              </span>
            </div>
          </div>
        </Card>
        <div class="h-[40%] flex-1">
          <div class="flex flex-col gap-2">
            <MedicalCard ref="medicalRecordCard" v-model="medicalTemplate" />
            <!-- 中药处方 -->
            <TCMPrescriptionCard
              ref="tcmPrescriptionCard"
              v-model="herbalPrescriptionFormData"
              @change="handleHerbalPrescriptionChange"
            />
            <!-- 中成药处方 -->
            <MedicinePrescriptionCard
              ref="chineseMedicinePrescriptionCard"
              v-model="chineseMedicinePrescriptionFormData"
              :category="MedicineCategory.ZCY"
              title="中成药处方"
              icon="icon-[material-symbols--prescriptions]"
              icon-color="text-success"
              @change="handleChineseMedicinePrescriptionChange"
            />
            <!-- 西药处方 -->
            <MedicinePrescriptionCard
              ref="westernMedicinePrescriptionCard"
              v-model="westernMedicinePrescriptionFormData"
              :category="MedicineCategory.XY"
              title="西药处方"
              icon="icon-[material-symbols--prescriptions]"
              icon-color="text-danger"
              @change="handleWesternMedicinePrescriptionChange"
            />

            <!-- 诊疗项目 -->
            <TreatmentProjectCard
              ref="treatmentProjectCard"
              v-model="treatmentProjectFormData"
              @change="handleTreatmentProjectChange"
            />
          </div>
        </div>
      </div>
      <div class="w-[20%]">
        <Card size="small" class="h-full overflow-y-auto">
          <template #title>
            <div class="flex items-center gap-2">
              <i class="icon-[material-symbols--history]"></i>
              <span>就诊历史</span>
            </div>
          </template>
          <MedicalRecordHistory
            ref="medicalRecordHistoryRef"
            v-if="currentPatient"
            :patient-id="currentPatient.id"
            :current-record-id="currentMedicalRecord?.id || null"
            :enable-autofill="true"
            :current-patient-id="currentPatient?.id"
            :is-attending="!!currentMedicalRecord?.id"
            :current-patient="currentPatient"
            @records-loaded="handleRecordsLoaded"
            @autofill-all="handleAutofillAll"
            @autofill-diagnosis="handleAutofillDiagnosis"
            @autofill-tcm="handleAutofillTcm"
            @autofill-chinese-medicine="handleAutofillChineseMedicine"
            @autofill-western-medicine="handleAutofillWesternMedicine"
            @autofill-treatment="handleAutofillTreatment"
          />
          <div
            v-else
            class="flex h-32 items-center justify-center text-gray-400"
          >
            <Empty
              description="请选择患者查看就诊历史"
              class="translate-y-[30vh]"
            >
              <template #image>
                <i
                  class="icon-[streamline-plump-color--file-search] h-[100px] w-[100px]"
                ></i>
              </template>
            </Empty>
          </div>
        </Card>
      </div>
    </div>

    <!-- 处方模板选择器 -->
    <TemplateSelector
      v-model:visible="showPrescriptionTemplateSelector"
      @select="handlePrescriptionTemplateSelect"
    />

    <!-- 支付弹窗 -->
    <PaymentModal
      v-model:open="showPaymentModal"
      ref="paymentModalRef"
      :preview="paymentPreview"
      :payment-items="paymentItems"
      @confirm="handlePaymentConfirm"
      @cancel="handlePaymentCancel"
    />

    <!-- 填充确认对话框 -->
    <AutofillConfirmModal
      v-model:visible="showAutofillConfirmModal"
      :fill-type="currentAutofillType"
      :has-existing-data="hasExistingDataForAutofill"
      :record="currentAutofillRecord"
      @confirm="handleConfirmModalConfirm"
      @cancel="handleConfirmModalCancel"
    />
  </Page>
</template>

<style scoped lang="scss"></style>
