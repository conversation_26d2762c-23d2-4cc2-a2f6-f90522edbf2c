import type { Ref } from 'vue';

export interface VxeKeyboardNavigationOptions {
  /** 表格数据 */
  tableData: Ref<any[]>;
  /** 表格实例引用 */
  tableRef: Ref<any>;
  /** 可编辑的字段列表 */
  editableFields: string[];
  /** 添加新行的方法 */
  addRow: () => void;
  /** 行数据的唯一标识字段，默认为 'id' */
  rowIdField?: string;
}

/**
 * VXE Table 键盘导航 Composable
 * 提供统一的键盘导航功能，支持方向键、Tab、Enter、空格键等
 */
export function useVxeKeyboardNavigation(options: VxeKeyboardNavigationOptions) {
  const {
    tableData,
    tableRef,
    editableFields,
    addRow,
    rowIdField = 'id'
  } = options;

  /**
   * 键盘导航处理函数
   * @param params VXE Table 的 keydown 事件参数
   */
  const handleKeyDown = async (params: any) => {
    const { $event } = params;
    const { key } = $event;

    // 从 VXE Table 获取当前编辑的单元格信息
    const editCell = tableRef.value?.getEditCell();
    if (!editCell) {
      return;
    }

    const { row, column } = editCell;
    if (!row || !column) {
      return;
    }

    const currentRowIndex = tableData.value.findIndex(
      (item) => item[rowIdField] === row[rowIdField],
    );
    const currentFieldIndex = editableFields.indexOf(column.field);

    // 边界检查：确保表格有数据且当前行索引有效
    if (currentRowIndex === -1 || tableData.value.length === 0) {
      return;
    }

    // 处理 Tab 键、Enter 键、空格键和方向键
    const validKeys = [
      'Tab',
      'Enter',
      ' ',
      'ArrowLeft',
      'ArrowUp',
      'ArrowRight',
      'ArrowDown',
    ];
    if (!validKeys.includes(key)) {
      return;
    }

    $event.preventDefault();
    $event.stopPropagation();
    let targetRowIndex = currentRowIndex;
    let targetFieldIndex = currentFieldIndex;

    // 导航到下一行或新增行
    const navigateToNextRow = () => {
      if (currentRowIndex < tableData.value.length - 1) {
        // 不是最后一行，跳到下一行
        return { targetRowIndex: currentRowIndex + 1, targetFieldIndex: 0 };
      } else {
        // 最后一行，新增一行
        addRow();
        // 等待DOM更新后再设置目标行索引
        setTimeout(() => {
          const newTargetRowIndex = tableData.value.length - 1;
          const targetField = editableFields[0]; // 新增行的第一列
          const targetRow = tableData.value[newTargetRowIndex];
          if (targetRow && targetField) {
            tableRef.value.setEditCell(targetRow, targetField);
          }
        }, 100);
        return null; // 表示不需要后续处理
      }
    };

    // 导航到上一行
    const navigateToPreviousRow = () => {
      if (currentRowIndex > 0) {
        return {
          targetRowIndex: currentRowIndex - 1,
          targetFieldIndex: currentFieldIndex,
        };
      }
      return null;
    };

    // 导航到下一列
    const navigateToNextColumn = () => {
      if (currentFieldIndex < editableFields.length - 1) {
        return {
          targetRowIndex: currentRowIndex,
          targetFieldIndex: currentFieldIndex + 1,
        };
      } else {
        // 已经是最后一列，跳到下一行第一列
        if (currentRowIndex < tableData.value.length - 1) {
          return { targetRowIndex: currentRowIndex + 1, targetFieldIndex: 0 };
        } else {
          // 最后一行，新增一行
          addRow();
          setTimeout(() => {
            const newTargetRowIndex = tableData.value.length - 1;
            const targetField = editableFields[0];
            const targetRow = tableData.value[newTargetRowIndex];
            if (targetRow && targetField) {
              tableRef.value.setEditCell(targetRow, targetField);
            }
          }, 100);
          return null;
        }
      }
    };

    // 导航到上一列
    const navigateToPreviousColumn = () => {
      if (currentFieldIndex > 0) {
        return {
          targetRowIndex: currentRowIndex,
          targetFieldIndex: currentFieldIndex - 1,
        };
      } else {
        // 已经是第一列，跳到上一行的最后一列
        if (currentRowIndex > 0) {
          return {
            targetRowIndex: currentRowIndex - 1,
            targetFieldIndex: editableFields.length - 1,
          };
        }
      }
      return null;
    };

    switch (key) {
      case ' ': {
        // 空格键：激活当前单元格编辑状态
        if (editableFields.includes(column.field)) {
          // 当前列是可编辑的，激活编辑状态
          await tableRef.value.setEditCell(row, column.field);
        }
        return; // 空格键不需要后续的跳转逻辑
      }
      case 'ArrowDown': {
        // 下方向键：跳到下一行的同一列
        const downResult = navigateToNextRow();
        if (downResult) {
          targetRowIndex = downResult.targetRowIndex;
          targetFieldIndex = downResult.targetFieldIndex;
        } else {
          return; // 已经处理了新增行
        }
        break;
      }
      case 'ArrowLeft': {
        // 左方向键：跳到左边的可编辑列
        const leftResult = navigateToPreviousColumn();
        if (leftResult) {
          targetRowIndex = leftResult.targetRowIndex;
          targetFieldIndex = leftResult.targetFieldIndex;
        }
        break;
      }
      case 'ArrowRight': {
        // 右方向键：跳到右边的可编辑列
        const rightResult = navigateToNextColumn();
        if (rightResult) {
          targetRowIndex = rightResult.targetRowIndex;
          targetFieldIndex = rightResult.targetFieldIndex;
        } else {
          return; // 已经处理了新增行
        }
        break;
      }
      case 'ArrowUp': {
        // 上方向键：跳到上一行的同一列
        const upResult = navigateToPreviousRow();
        if (upResult) {
          targetRowIndex = upResult.targetRowIndex;
          targetFieldIndex = upResult.targetFieldIndex;
        }
        break;
      }
      case 'Enter': {
        // Enter键：跳到下一行或者新增下一行
        const enterResult = navigateToNextRow();
        if (enterResult) {
          targetRowIndex = enterResult.targetRowIndex;
          targetFieldIndex = enterResult.targetFieldIndex;
        } else {
          return; // 已经处理了新增行
        }
        break;
      }
      case 'Tab': {
        // Tab 键：切换到下一个可编辑列
        const tabResult = navigateToNextColumn();
        if (tabResult) {
          targetRowIndex = tabResult.targetRowIndex;
          targetFieldIndex = tabResult.targetFieldIndex;
        } else {
          return; // 已经处理了新增行
        }
        break;
      }
    }

    const targetField = editableFields[targetFieldIndex];
    const targetRow = tableData.value[targetRowIndex];
    if (targetRow && targetField) {
      tableRef.value.setEditCell(targetRow, targetField);
    }
  };

  return {
    handleKeyDown,
  };
}
