import type { MedicinePrescriptionData } from '../../medicine-prescription-table';
import type { HerbalPrescriptionData } from '../../tcm-prescription-table';

import type { MedicalTemplateVO } from '#/api/clinic/medical/template/model';
import type { PrintType } from '#/constants/qili-common-enum';

export interface PrescriptionData {
  printType: PrintType;
  patientId: number | string;
  // 病历
  medicalField: MedicalTemplateVO;
  // 就诊号
  visitNo: number | string | undefined;
  visitTime: string | undefined;
  // 总金额
  totalAmount: number;
  // 中药处方数据
  tcmPrescriptionData?: HerbalPrescriptionData;
  // 中成药处方数据
  zcyPrescriptionData?: MedicinePrescriptionData;
  // 西药处方数据
  xyPrescriptionData?: MedicinePrescriptionData;
}

export interface PrintData {
  pharmacyName: string;
  prescriptionData: PrescriptionData;
}
