<script setup lang="ts">
import type { PrescriptionData, PrintData } from './typing';

import type { PatientVO } from '#/api/clinic/patient/model';
import type { PrintSetting } from '#/api/clinic/userSetting/model';

import { computed, nextTick, onMounted, ref, watch } from 'vue';
import { VuePrintNext } from 'vue-print-next';

import { useUserStore } from '@vben/stores';

import dayjs from 'dayjs';

import { patientInfo } from '#/api/clinic/patient';
import { userSettingInfoWithCurrentUser } from '#/api/clinic/userSetting';
import { PrintType } from '#/constants/qili-common-enum';
import { QiliDictEnum } from '#/constants/qili-dict-enum';
import { getDictLabel } from '#/utils/dict';

interface Props {
  data: PrescriptionData;
  visible: boolean;
}

const props = defineProps<Props>();
const emit = defineEmits<{
  'update:visible': [value: boolean];
}>();

const printContentRef = ref<HTMLElement | null>(null);
const userStore = useUserStore();
const medicalRecordFields = ref<{ label: string; value: string | undefined }[]>(
  [],
);
const patientData = ref<null | PatientVO>(null);
const printData = ref<PrintData>({
  pharmacyName: '诊所',
  prescriptionData: props.data,
});

const printSetting = ref<PrintSetting>({
  pharmacyName: '',
  medicalRecordFields: [],
  pagerSize: 5,
  printerAddress: '',
  isShowSingleMedicineWeight: true,
  isShowTotalWeight: true,
});
const checkedSettingFields = ref<string[]>([]);

// 获取患者信息
const fetchPatientInfo = async (patientId: number | string) => {
  try {
    const patient = await patientInfo(patientId);
    patientData.value = patient;
  } catch (error) {
    console.error('获取患者信息失败:', error);
    patientData.value = null;
  }
};

onMounted(async () => {
  // 获取药店名称
  const settingRes = await userSettingInfoWithCurrentUser();
  const parsedSetting = JSON.parse(settingRes.printSetting);
  printSetting.value = { ...printSetting.value, ...parsedSetting };
  printData.value.pharmacyName = printSetting.value.pharmacyName;
  checkedSettingFields.value = printSetting.value.medicalRecordFields;

  // 获取患者信息
  if (props.data.patientId) {
    await fetchPatientInfo(props.data.patientId);
  }
});

// 病历字段和值
const getMedicalRecordFields = () => {
  if (!printData.value?.prescriptionData?.medicalField?.fieldList) {
    return [];
  }
  medicalRecordFields.value =
    printData.value.prescriptionData.medicalField.fieldList
      .filter((field) => checkedSettingFields.value?.includes(field.fieldName))
      .map((field) => {
        return {
          label: field.fieldName,
          value: field.fieldValue,
        };
      });
};
watch(
  () => props.data,
  async (newData) => {
    printData.value.prescriptionData = newData;
    // 获取患者信息
    getMedicalRecordFields();
  },
  { deep: true },
);
// 监听visible变化，触发打印预览
watch(
  () => props.visible,
  async (newVisible) => {
    if (newVisible) {
      await fetchPatientInfo(props.data.patientId);
      await nextTick();
      if (printContentRef.value) {
        const printInstance = new VuePrintNext({
          el: printContentRef.value,
          paperSize: 'A5',
          closeCallback: () => {
            emit('update:visible', false);
          },
        });
        getMedicalRecordFields();
        printInstance.print();
      }
    }
  },
);

const totalPrice = computed(() => {
  return () => {
    switch (printData.value.prescriptionData.printType) {
      case PrintType.CHINESE_MEDICINE: {
        const zcyPrescriptionData =
          printData.value.prescriptionData.zcyPrescriptionData;
        if (!zcyPrescriptionData) {
          return 0;
        }
        return zcyPrescriptionData.tableData
          .reduce((acc, drug) => {
            return acc + drug.price * drug.totalAmount;
          }, 0)
          .toFixed(2);
      }
      case PrintType.TCM_PRESCRIPTION: {
        const tcmPrescriptionData =
          printData.value.prescriptionData.tcmPrescriptionData;
        if (!tcmPrescriptionData) {
          return 0;
        }
        return tcmPrescriptionData.tableData
          .reduce((acc, drug) => {
            return acc + drug.subtotal * tcmPrescriptionData.dosage;
          }, 0)
          .toFixed(2);
      }
      case PrintType.WESTERN_MEDICINE: {
        const xyPrescriptionData =
          printData.value.prescriptionData.xyPrescriptionData;
        if (!xyPrescriptionData) {
          return 0;
        }
        return xyPrescriptionData.tableData
          .reduce((acc, drug) => {
            return acc + drug.price * drug.totalAmount;
          }, 0)
          .toFixed(2);
      }
    }
  };
});
</script>

<template>
  <div
    v-if="visible"
    ref="printContentRef"
    class="prescription-print flex h-screen w-screen flex-col justify-between bg-white p-10 text-sm leading-relaxed text-gray-900"
  >
    <div>
      <!-- 处方头部 -->
      <div class="mb-4 text-center">
        <h1 class="text-xl font-bold">
          {{ printData.pharmacyName || '诊所' }}
        </h1>
        <div class="mt-1 text-base">处方笺</div>
      </div>

      <!-- 基本信息 -->
      <div
        class="mb-2 grid gap-2 border-b border-dashed border-gray-600 pb-4 text-sm"
      >
        <div class="grid grid-cols-3 gap-1">
          <div>
            日期：{{
              dayjs(printData.prescriptionData.visitTime).format('YYYY-MM-DD')
            }}
          </div>
          <div></div>
          <div>诊号：{{ printData.prescriptionData.visitNo }}</div>
        </div>
        <div class="grid grid-cols-12 gap-1">
          <div class="col-span-3">姓名：{{ patientData?.name || '' }}</div>
          <div class="col-span-2">
            性别：{{
              getDictLabel(
                QiliDictEnum.QILI_PATIENT_GENDER,
                patientData?.gender,
              )
            }}
          </div>
          <div class="col-span-3">年龄：{{ patientData?.ageYear || '' }}</div>
          <div class="col-span-4">手机号：{{ patientData?.phone || '' }}</div>
        </div>
        <!-- 根据打印设置的勾选显示 -->
        <div v-for="field in medicalRecordFields" :key="field.label">
          {{ field.label }}：{{ field.value }}
        </div>
      </div>
      <div class="mb-3">
        <div class="pb-1 font-bold">Rp.</div>
        <!-- 中药处方药品信息 -->
        <div
          v-if="
            printData.prescriptionData.printType === PrintType.TCM_PRESCRIPTION
          "
        >
          <div class="mt-2 grid grid-cols-5 text-sm">
            <div
              v-for="drug in printData.prescriptionData?.tcmPrescriptionData
                ?.tableData"
              :key="drug.id"
              class="mt-1 gap-1"
            >
              <span class="mr-1">{{ drug.name }}</span>
              <span v-show="printSetting.isShowSingleMedicineWeight">
                {{ drug.amount }}{{ drug.unit }}
              </span>
            </div>
          </div>
          <div class="mt-3">
            <p>
              <span>用法：共 </span>
              <span>
                {{ printData?.prescriptionData?.tcmPrescriptionData?.dosage }}
                剂，
              </span>
              <span>
                {{ printData?.prescriptionData?.tcmPrescriptionData?.usage }}，
              </span>
              <span>
                {{
                  getDictLabel(
                    QiliDictEnum.QILI_DRUG_FREQUENCY,
                    printData?.prescriptionData?.tcmPrescriptionData?.frequency,
                  )
                }}，
              </span>
              <span>
                每次
                {{
                  printData?.prescriptionData?.tcmPrescriptionData?.usageValue
                }}
                ml
              </span>
            </p>
          </div>
          <div
            v-if="printData.prescriptionData?.tcmPrescriptionData?.prescription"
            class="flex"
          >
            <div>医嘱：</div>
            <div>
              {{
                printData.prescriptionData?.tcmPrescriptionData?.prescription
              }}
            </div>
          </div>
        </div>
        <!-- 中成药处方药品信息 -->
        <div
          v-else-if="
            printData.prescriptionData.printType === PrintType.CHINESE_MEDICINE
          "
          class="mt-2 grid grid-cols-1 gap-y-1 text-sm"
        >
          <div
            v-for="drug in printData.prescriptionData?.zcyPrescriptionData
              ?.tableData"
            :key="drug.id"
            class="flex"
          >
            <span class="mr-2">{{ drug.name }}</span>
            <span>
              {{ getDictLabel(QiliDictEnum.QILI_DRUG_USAGE, drug.usage) }}，
            </span>
            <span>
              {{
                getDictLabel(QiliDictEnum.QILI_DRUG_FREQUENCY, drug.frequency)
              }}，
            </span>
            <span>每次 {{ `${drug.dosage} ${drug.unit}` }}，</span>
            <span>共{{ drug.days }}天</span>
          </div>
          <div
            v-if="printData.prescriptionData?.zcyPrescriptionData?.prescription"
            class="mt-3 flex"
          >
            <div>医嘱：</div>
            <div>
              {{
                printData.prescriptionData?.zcyPrescriptionData?.prescription
              }}
            </div>
          </div>
        </div>
        <!-- 西药处方药品信息 -->
        <div
          v-else-if="
            printData.prescriptionData.printType === PrintType.WESTERN_MEDICINE
          "
          class="mt-2 grid grid-cols-1 gap-y-1 text-sm"
        >
          <div
            v-for="drug in printData.prescriptionData?.xyPrescriptionData
              ?.tableData"
            :key="drug.id"
          >
            <span class="mr-2">{{ drug.name }}</span>
            <span>
              {{ getDictLabel(QiliDictEnum.QILI_DRUG_USAGE, drug.usage) }}，
            </span>
            <span>
              {{
                getDictLabel(QiliDictEnum.QILI_DRUG_FREQUENCY, drug.frequency)
              }}，
            </span>
            <span>每次 {{ `${drug.dosage} ${drug.unit}` }}，</span>
            <span>共{{ drug.days }}天</span>
          </div>
          <div
            v-if="printData.prescriptionData?.xyPrescriptionData?.prescription"
            class="mt-3 flex"
          >
            <div>医嘱：</div>
            <div>
              {{
                printData.prescriptionData?.zcyPrescriptionData?.prescription
              }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 签名与金额 -->
    <div
      class="flex items-center justify-between border-t border-gray-600 pt-2"
    >
      <div class="flex items-center gap-1">
        <label>医生：</label>
        <img :src="userStore.userInfo?.esign" class="w-35 h-8" />
      </div>
      <div>金额：￥{{ totalPrice() }}</div>
      <div>
        <label>打印时间：</label>
        <span>{{ dayjs(new Date()).format('YYYY-MM-DD HH:mm:ss') }}</span>
      </div>
    </div>
  </div>
</template>

<style scoped>
@media print {
  .prescription-print {
    /* padding: 20mm; */
  }
}

.prescription-print {
}
</style>
