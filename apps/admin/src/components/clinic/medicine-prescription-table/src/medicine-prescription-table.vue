<script setup lang="ts">
import type { VxeTablePropTypes } from 'vxe-table';

import type {
  MedicinePrescriptionData,
  MedicinePrescriptionItem,
} from './typing';

import type { DrugVO } from '#/api/clinic/pharmacy/drug/model';

import { computed, ref, watch } from 'vue';

import { buildShortUUID } from '@vben/utils';

import { Button, Input, Select } from 'ant-design-vue';

import { DrugSelect } from '#/components/clinic/drug-select';
import { MedicineCategory } from '#/constants/qili-common-enum';
import { QiliDictEnum } from '#/constants/qili-dict-enum';
import { validTableEditRules } from '#/utils/cis-common';
import { getDictLabel, getDictOptions } from '#/utils/dict';

interface Props {
  maxHeight?: string;
  modelValue?: MedicinePrescriptionData;
  category: MedicineCategory; // 药品类别：ZCY(中成药) 或 XY(西药)
  disabled?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  maxHeight: '250px',
  disabled: false,
  modelValue: () => ({
    tableData: [],
    prescription: '',
  }),
});

const emit = defineEmits<{
  change: [value: MedicinePrescriptionData];
  'update:modelValue': [value: MedicinePrescriptionData];
}>();

const { Option } = Select;

const usageOptions = computed(() => {
  return getDictOptions(QiliDictEnum.QILI_DRUG_USAGE);
});

const frequencyOptions = computed(() => {
  return getDictOptions(QiliDictEnum.QILI_DRUG_FREQUENCY);
});

// 初始化本地数据（药品处方）
const tableData = ref<MedicinePrescriptionItem[]>(
  props.modelValue?.tableData || [],
);
const prescription = ref(props.modelValue?.prescription || '');

// 监听props变化，更新本地数据
watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue) {
      tableData.value = newValue.tableData || [];
      prescription.value = newValue.prescription || '';
    }
  },
  { deep: true },
);

// 计算当前完整数据
const currentData = computed(
  (): MedicinePrescriptionData => ({
    tableData: tableData.value,
    prescription: prescription.value,
  }),
);

// 发送数据变化
const emitChange = () => {
  const data = currentData.value;
  emit('update:modelValue', data);
  emit('change', data);
};

// 监听各个字段变化，发送到父组件
watch(
  [tableData, prescription],
  () => {
    emitChange();
  },
  { deep: true },
);

const editConfig = computed<VxeTablePropTypes.EditConfig>(() => ({
  trigger: 'click',
  mode: 'cell',
  autoFocus: true,
  showStatus: true,
  showUpdateStatus: true,
  showInsertStatus: true,
  enabled: !props.disabled,
}));

const keyboardConfig = computed<VxeTablePropTypes.KeyboardConfig>(() => ({
  isEdit: true, // 进入/切换编辑
  isTab: true, // Tab 导航
  isEnter: true, // Enter 导航
  isArrow: true, // 方向键（可选）
}));

// vxe 校验规则
const editRules = ref<VxeTablePropTypes.EditRules>({
  name: [{ required: true, message: '请选择药品', trigger: 'change' }],
  usage: [{ required: true, message: '请选择用法', trigger: 'change' }],
  frequency: [{ required: true, message: '请选择频率', trigger: 'change' }],
  dosage: [
    { required: true, message: '请输入剂量', trigger: 'blur' },
    {
      validator({ cellValue }) {
        const v = Number(cellValue);
        if (Number.isNaN(v) || v <= 0) {
          return new Error('剂量必须大于 0');
        }
      },
      trigger: 'blur',
    },
  ],
  days: [
    { required: true, message: '请输入天数', trigger: 'blur' },
    {
      validator({ cellValue }) {
        const v = Number(cellValue);
        if (!Number.isInteger(v) || v <= 0) {
          return new Error('天数必须为大于 0 的整数');
        }
      },
      trigger: 'blur',
    },
  ],
});

// 表实例与对外暴露校验方法
const tableRef = ref();

const validate = async () => {
  // 没有数据则跳过校验，认为通过
  if (!tableData.value || tableData.value.length === 0) {
    return true;
  }
  try {
    const tableErrMap = await tableRef.value.validate(true);
    if (
      !validTableEditRules(
        tableErrMap,
        props.category === MedicineCategory.ZCY ? '中成药处方' : '西药处方',
      )
    ) {
      return false;
    }
    return true;
  } catch {
    return false;
  }
};

const addRow = () => {
  tableData.value.push({
    id: buildShortUUID(),
    name: '',
    usage: 'po',
    frequency: 'tid',
    dosage: '',
    days: 1,
    totalAmount: 0,
    price: 0,
    code: '',
    unit: '',
    spec: '',
  });
};

// 处理药品选择
const handleDrugSelect = (drug: DrugVO, row: MedicinePrescriptionItem) => {
  row.id = drug.id;
  row.name = drug.name;
  row.code = drug.code;
  row.price = Number(drug.priceYuan);
  row.unit = drug.unit;
  row.spec = (drug as any).spec || '-'; // 规格字段可能不存在于DrugVO中
  // 计算总量：剂量 * 频次 * 天数
  updateTotalAmount(row);
};

const frequencyMap: Record<string, number> = {
  qd: 1,
  bid: 2, // 每日2次
  tid: 3, // 每日3次
  qid: 4, // 每日4次
  qn: 1, // 每晚1次
  qh: 24, // 每小时1次
};

// 计算总量
const updateTotalAmount = (row: MedicinePrescriptionItem) => {
  if (!row.frequency) {
    row.totalAmount = 0;
    return;
  }
  const frequencyNum = frequencyMap[row.frequency.toLowerCase()] || 1;
  row.totalAmount = Number(row.dosage) * frequencyNum * row.days;
};

const removeRow = (row: MedicinePrescriptionItem) => {
  const index = tableData.value.findIndex((item) => item.id === row.id);
  if (index !== -1) {
    tableData.value.splice(index, 1);
  }
};

const totalPrice = computed(() => {
  return tableData.value
    .reduce((sum, item) => sum + (item.price * item.totalAmount || 0), 0)
    .toFixed(2);
});

const drugCount = computed(() => {
  return tableData.value.length;
});

// 清空表单数据
const clearData = () => {
  tableData.value = [];
  prescription.value = '';
};

// 设置表单数据
const setData = (data: Partial<MedicinePrescriptionData>) => {
  if (data.tableData !== undefined) tableData.value = data.tableData;
  if (data.prescription !== undefined) prescription.value = data.prescription;
};
/**
 * 获取字典映射关系
 * @returns 字典映射关系
 */
const getDictMapping = (): Record<string, string> => {
  return {
    usage: QiliDictEnum.QILI_DRUG_USAGE,
    frequency: QiliDictEnum.QILI_DRUG_FREQUENCY,
    unit: QiliDictEnum.QILI_DRUG_UNIT,
  };
};

// 暴露方法给父组件
defineExpose({
  addRow,
  clearData,
  setData,
  getData: () => currentData.value,
  getDictMapping,
  validate,
});
</script>

<template>
  <div>
    <vxe-table
      ref="tableRef"
      :data="tableData"
      :edit-config="editConfig"
      :edit-rules="editRules"
      :valid-config="{ message: 'tooltip' }"
      border
      stripe
      size="small"
      align="center"
      :max-height="maxHeight"
      :keyboard-config="keyboardConfig"
    >
      <vxe-column type="seq" width="50" title="序号" />
      <vxe-column
        field="name"
        title="药品名称"
        min-width="150"
        :edit-render="{ autoFocus: true, placeholder: '请点击选择药品...' }"
      >
        <template #edit="{ row }">
          <DrugSelect
            v-model:value="row.name"
            :category="category"
            class="w-full"
            :disabled="props.disabled"
            @select="(drug) => handleDrugSelect(drug, row)"
          />
        </template>
      </vxe-column>
      <vxe-column
        field="usage"
        title="用法"
        width="120"
        :edit-render="{
          placeholder: '请点击选择',
        }"
      >
        <template #edit="{ row }">
          <Select
            v-model:value="row.usage"
            class="w-full"
            size="small"
            placeholder="请选择"
            :disabled="props.disabled"
          >
            <Option
              v-for="item in usageOptions"
              :key="item.value"
              :value="item.value"
            >
              {{ item.label }}
            </Option>
          </Select>
        </template>
        <template #default="{ row }">
          <span v-if="row.usage" class="text-gray-900">
            {{ getDictLabel(QiliDictEnum.QILI_DRUG_USAGE, row.usage) }}
          </span>
          <span v-else class="vxe-cell--placeholder">点击选择用法</span>
        </template>
      </vxe-column>
      <vxe-column
        field="frequency"
        title="频率"
        width="150"
        :edit-render="{
          placeholder: '点击此处选择',
        }"
      >
        <template #edit="{ row }">
          <Select
            v-model:value="row.frequency"
            class="w-full"
            size="small"
            placeholder="请选择"
            :disabled="props.disabled"
            @change="updateTotalAmount(row)"
          >
            <Option
              v-for="item in frequencyOptions"
              :key="item.value"
              :value="item.value"
            >
              {{ item.label }}
            </Option>
          </Select>
        </template>
        <template #default="{ row }">
          <span v-if="row.frequency" class="text-gray-900">
            {{ getDictLabel(QiliDictEnum.QILI_DRUG_FREQUENCY, row.frequency) }}
          </span>
          <span v-else class="vxe-cell--placeholder">点击选择频率</span>
        </template>
      </vxe-column>
      <vxe-column
        field="dosage"
        title="剂量"
        width="100"
        :edit-render="{
          placeholder: '点击此处输入',
        }"
      >
        <template #edit="{ row }">
          <Input
            v-model:value="row.dosage"
            placeholder="请输入"
            class="w-full text-center"
            size="small"
            :disabled="props.disabled"
            @blur="updateTotalAmount(row)"
          >
            <template #suffix>
              <span class="text-xs text-gray-400">{{ row.unit }}</span>
            </template>
          </Input>
        </template>
        <template #default="{ row }">
          <span v-if="row.dosage" class="text-gray-900">
            <span class="mr-1">{{ row.dosage }}</span>
            <span class="text-xs text-gray-400"> {{ row.unit }} </span>
          </span>
          <span v-else class="vxe-cell--placeholder">点击输入剂量</span>
        </template>
      </vxe-column>
      <vxe-column
        field="days"
        title="天数"
        width="80"
        :edit-render="{
          placeholder: '点击此处输入',
        }"
      >
        <template #edit="{ row }">
          <Input
            v-model:value="row.days"
            type="number"
            placeholder="天数"
            class="w-full text-center"
            size="small"
            :disabled="props.disabled"
            @blur="updateTotalAmount(row)"
          >
            <template #suffix>
              <span class="text-xs text-gray-400">天</span>
            </template>
          </Input>
        </template>
        <template #default="{ row }">
          <span v-if="row.days" class="text-gray-900">
            <span class="mr-1">{{ row.days }}</span>
            <span class="text-xs text-gray-400">天</span>
          </span>
          <span v-else class="vxe-cell--placeholder">点击输入天数</span>
        </template>
      </vxe-column>
      <vxe-column field="totalAmount" title="总量" width="80">
        <template #default="{ row }">
          <span class="mr-1">{{ row.totalAmount }}</span>
          <span class="text-xs text-gray-400"> {{ row.unit }} </span>
        </template>
      </vxe-column>
      <vxe-column field="price" title="单价" width="80">
        <template #default="{ row }">
          <span class="mr-1">¥{{ row.price.toFixed(2) }}</span>
          <span v-if="row.unit" class="text-xs text-gray-400">
            /{{ row.unit }}
          </span>
        </template>
      </vxe-column>
      <vxe-column field="totalPrice" title="小计" width="80">
        <template #default="{ row }">
          ¥{{ (row.price * row.totalAmount).toFixed(2) }}
        </template>
      </vxe-column>
      <vxe-column v-if="!props.disabled" title="操作" width="80" fixed="right">
        <template #default="{ row }">
          <Button
            type="link"
            danger
            size="small"
            class="flex items-center"
            :disabled="props.disabled"
            @click="removeRow(row)"
          >
            <i
              class="icon-[material-symbols-light--delete-outline-rounded] mr-1"
            ></i>
            删除
          </Button>
        </template>
      </vxe-column>
    </vxe-table>

    <div class="mt-2 flex select-none rounded-md bg-gray-100 p-2 text-sm">
      <div class="flex w-full items-center justify-start text-sm">
        <label class="w-[3rem]">医嘱:</label>
        <Input
          v-model:value="prescription"
          :placeholder="disabled ? '' : '请输入医嘱'"
          allow-clear
          size="small"
          class="ml-[1px] w-[50%]"
          :disabled="props.disabled"
        />
      </div>
    </div>

    <div class="mt-4 flex select-none justify-end text-xs">
      <div class="mr-2 flex text-gray-400">
        <label>药品数量：</label>
        <div class="text-primary">
          {{ drugCount }}
        </div>
        <span class="ml-1">种</span>
      </div>
      <div class="flex text-gray-400">
        <label>总价：</label>
        <div class="text-primary">
          {{ totalPrice }}
        </div>
        <span class="ml-1">元</span>
      </div>
    </div>
  </div>
</template>

<style scoped>
:deep(.vxe-table) {
  border-radius: 8px;
}
</style>
