<script setup lang="ts">
import type { MedicalFieldDataVO } from '#/api/clinic/medical/field-data/model';

import { computed, ref, watch, watchEffect } from 'vue';

import {
  Button,
  CheckableTag,
  Empty,
  InputSearch,
  message,
  Modal,
} from 'ant-design-vue';

import { medicalFieldDataList } from '#/api/clinic/medical/field-data';
import { CisTitle } from '#/components/clinic/cis-title';

interface Props {
  visible: boolean;
  currentRow: any;
  selectedValues: string[];
}

const props = defineProps<Props>();

const emit = defineEmits<{
  cancel: [];
  confirm: [values: string[]];
  'update:visible': [visible: boolean];
}>();

const searchKeyword = ref('');
const localSelectedValues = ref<string[]>([]);
const fieldDataList = ref<MedicalFieldDataVO[]>([]);
const loading = ref(false);

const loadFieldDataList = async () => {
  if (!props.currentRow?.id) return;

  loading.value = true;
  try {
    const response = await medicalFieldDataList({
      fieldId: props.currentRow.id,
      pageNum: 1,
      pageSize: 1000, // 获取所有数据
    });

    fieldDataList.value = response.rows || [];
  } catch (error) {
    console.error('获取字段数据列表失败:', error);
    message.error('获取字段数据列表失败');
    fieldDataList.value = [];
  } finally {
    loading.value = false;
  }
};

watchEffect(() => {
  if (props.visible) {
    localSelectedValues.value = [...props.selectedValues];
    searchKeyword.value = '';
  }
});

// 监听弹窗显示状态，显示时加载数据
watch(
  () => props.visible,
  async (newVisible) => {
    if (newVisible && props.currentRow?.id) {
      await loadFieldDataList();
    }
  },
  { immediate: true },
);

const getGroupedData = (fieldDataList: MedicalFieldDataVO[]) => {
  const groups: Record<string, MedicalFieldDataVO[]> = {};
  const filteredList =
    fieldDataList?.filter(
      (item) =>
        !searchKeyword.value ||
        item.data.toLowerCase().includes(searchKeyword.value.toLowerCase()),
    ) || [];

  for (const item of filteredList) {
    const groupName = item.groupName || '其他';
    if (!groups[groupName]) {
      groups[groupName] = [];
    }
    groups[groupName].push(item);
  }
  return groups;
};

const handleTagSelect = (item: MedicalFieldDataVO, checked: boolean) => {
  if (checked) {
    if (!localSelectedValues.value.includes(item.data)) {
      localSelectedValues.value.push(item.data);
    }
  } else {
    const index = localSelectedValues.value.indexOf(item.data);
    if (index !== -1) {
      localSelectedValues.value.splice(index, 1);
    }
  }
};

const handleCancel = () => {
  emit('cancel');
  emit('update:visible', false);
};

const handleConfirm = () => {
  emit('confirm', localSelectedValues.value);
  emit('update:visible', false);
};

const hasFieldDataList = computed(() => (fieldDataList.value?.length || 0) > 0);
const hasGroupedData = computed(
  () => Object.keys(getGroupedData(fieldDataList.value || [])).length > 0,
);
</script>

<template>
  <Modal
    v-if="currentRow"
    :open="visible"
    :title="`快速选择${currentRow.fieldName}`"
    width="50%"
    destroy-on-close
    :body-style="{ height: '600px', overflow: 'hidden' }"
    centered
    :mask-closable="false"
    @update:open="(val) => emit('update:visible', val)"
    @cancel="handleCancel"
  >
    <div class="flex h-full w-full flex-col">
      <InputSearch
        placeholder="请输入关键词搜索"
        allow-clear
        enter-button
        class="mb-4"
        v-model:value="searchKeyword"
      />
      <div v-if="loading" class="flex flex-1 items-center justify-center">
        <div class="text-gray-400">加载中...</div>
      </div>
      <div v-else-if="hasFieldDataList" class="flex-1 overflow-auto">
        <div v-if="hasGroupedData" class="flex flex-col">
          <div
            v-for="(items, groupName) in getGroupedData(fieldDataList || [])"
            :key="groupName"
            class="mt-2 flex flex-col"
          >
            <CisTitle :title="groupName" size="base" />
            <div class="flex flex-wrap">
              <CheckableTag
                v-for="item in items"
                :key="item.id"
                :checked="localSelectedValues.includes(item.data)"
                class="mb-2 mr-2 text-sm"
                @change="(checked: boolean) => handleTagSelect(item, checked)"
              >
                {{ item.data }}
              </CheckableTag>
            </div>
          </div>
        </div>
        <Empty v-else description="无匹配结果" />
      </div>
      <Empty v-else description="暂无数据" />
    </div>
    <template #footer>
      <div class="flex items-center justify-between">
        <div class="text-sm text-gray-400">
          已选择:
          <span class="text-primary">{{ localSelectedValues.length }}</span>
          项
        </div>
        <div>
          <Button @click="handleCancel">取消</Button>
          <Button type="primary" class="ml-2" @click="handleConfirm">
            确定
          </Button>
        </div>
      </div>
    </template>
  </Modal>
</template>
