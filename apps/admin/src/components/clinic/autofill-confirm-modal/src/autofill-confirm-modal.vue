<script setup lang="ts">
import type { MedicalRecordVO } from '#/api/clinic/medical/record/model';

import { computed } from 'vue';

import {
  Alert,
  Button,
  Descriptions,
  DescriptionsItem,
  Modal,
} from 'ant-design-vue';

import { AutofillType, FormFillService } from '#/utils/clinic';

interface Props {
  visible: boolean;
  fillType: AutofillType;
  hasExistingData: boolean;
  record?: MedicalRecordVO | null;
}

interface Emits {
  'update:visible': [visible: boolean];
  confirm: [];
  cancel: [];
}

const props = withDefaults(defineProps<Props>(), {
  record: null,
});

const emit = defineEmits<Emits>();

// 计算对话框标题
const modalTitle = computed(() => {
  const typeName = FormFillService.getTypeName(props.fillType);
  return props.hasExistingData ? `确认覆盖${typeName}` : `确认填充${typeName}`;
});

// 计算提示信息
const alertMessage = computed(() => {
  const typeName = FormFillService.getTypeName(props.fillType);
  return props.hasExistingData
    ? `当前表单中已有数据，填充操作将覆盖现有数据，此操作不可撤销。`
    : `即将从历史记录中填充${typeName}数据到当前表单。`;
});

// 计算警告类型
const alertType = computed(() => {
  return props.hasExistingData ? 'warning' : 'info';
});

// 获取填充内容描述
const getFillDescription = (type: AutofillType): string => {
  const descriptions = {
    [AutofillType.ALL]: '病历诊断、中药处方、中成药处方、西药处方、诊疗项目',
    [AutofillType.DIAGNOSIS]: '病历诊断信息（主诉、现病史、既往史等）',
    [AutofillType.TCM_PRESCRIPTION]: '中药饮片处方（药品清单、剂数、用法等）',
    [AutofillType.CHINESE_MEDICINE]: '中成药处方（药品清单、用法用量等）',
    [AutofillType.WESTERN_MEDICINE]: '西药处方（药品清单、用法用量等）',
    [AutofillType.TREATMENT_PROJECT]: '诊疗项目（项目清单、时长、费用等）',
  };

  return descriptions[type] || type;
};

// 处理确认
const handleConfirm = () => {
  emit('confirm');
};

// 处理取消
const handleCancel = () => {
  emit('update:visible', false);
  emit('cancel');
};

// 处理对话框关闭
const handleModalCancel = () => {
  handleCancel();
};
</script>

<template>
  <Modal
    :open="visible"
    :title="modalTitle"
    :width="600"
    :closable="true"
    :mask-closable="true"
    @cancel="handleModalCancel"
  >
    <div class="space-y-4">
      <!-- 警告提示 -->
      <Alert
        :message="alertMessage"
        :type="alertType"
        show-icon
        :closable="false"
      />

      <!-- 历史记录信息 -->
      <div v-if="record" class="rounded bg-gray-50 p-4">
        <h4 class="mb-3 text-sm font-medium text-gray-700">历史记录信息</h4>
        <Descriptions size="small" :column="2" bordered>
          <DescriptionsItem label="诊号">
            {{ record.visitNo }}
          </DescriptionsItem>
          <DescriptionsItem label="就诊时间">
            {{ record.visitTime || '--' }}
          </DescriptionsItem>
          <DescriptionsItem label="接诊医生">
            {{ record.doctorName || '--' }}
          </DescriptionsItem>
          <DescriptionsItem label="患者姓名">
            {{ record.patientName || '--' }}
          </DescriptionsItem>
        </Descriptions>
      </div>

      <!-- 填充内容说明 -->
      <div class="rounded bg-blue-50 p-4">
        <h4 class="mb-2 text-sm font-medium text-gray-700">将要填充的内容</h4>
        <p class="text-sm text-gray-600">
          {{ getFillDescription(fillType) }}
        </p>
      </div>

      <!-- 注意事项 -->
      <div v-if="hasExistingData" class="rounded bg-orange-50 p-4">
        <h4 class="mb-2 text-sm font-medium text-orange-700">
          <i class="icon-[material-symbols--warning] mr-1"></i>
          注意事项
        </h4>
        <ul class="space-y-1 text-sm text-orange-600">
          <li>• 填充操作将完全覆盖当前表单中的数据</li>
          <li>• 此操作无法撤销，请确认后再继续</li>
          <li>• 建议在填充前保存当前重要数据</li>
        </ul>
      </div>

      <!-- 快捷键提示 -->
      <div v-if="!hasExistingData" class="rounded bg-blue-50 p-3">
        <div class="flex items-center gap-2 text-sm text-blue-600">
          <i class="icon-[material-symbols--keyboard]"></i>
          <span>提示：您也可以使用快捷键 Ctrl+Shift+A 快速填充最新记录</span>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="flex justify-end gap-2">
        <Button @click="handleCancel"> 取消 </Button>
        <Button
          type="primary"
          :danger="hasExistingData"
          @click="handleConfirm"
          class="flex items-center"
        >
          <i class="icon-[material-symbols--check] mr-1"></i>
          {{ hasExistingData ? '确认覆盖' : '确认填充' }}
        </Button>
      </div>
    </template>
  </Modal>
</template>

<style scoped>
.space-y-4 > * + * {
  margin-top: 1rem;
}

.space-y-1 > * + * {
  margin-top: 0.25rem;
}
</style>
