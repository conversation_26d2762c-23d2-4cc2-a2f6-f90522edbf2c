<script setup lang="ts">
import type { VxeTablePropTypes } from 'vxe-table';

import type { TreatmentProjectData, TreatmentProjectItem } from './typing';

import type { TreatmentProjectVO } from '#/api/clinic/treatmentProject/model';

import { computed, onMounted, ref, watch } from 'vue';

import { buildShortUUID } from '@vben/utils';

import { useDebounceFn } from '@vueuse/core';
import { Button, Input, Select, Spin } from 'ant-design-vue';

import { treatmentProjectList } from '#/api/clinic/treatmentProject';
import { QiliDictEnum } from '#/constants/qili-dict-enum';
import { validTableEditRules } from '#/utils/cis-common';
import { getDictLabel } from '#/utils/dict';

interface Props {
  maxHeight?: string;
  modelValue?: TreatmentProjectData;
  disabled?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  maxHeight: '250px',
  disabled: false,
  modelValue: () => ({
    tableData: [],
    prescription: '',
  }),
});

const emit = defineEmits<{
  change: [value: TreatmentProjectData];
  'update:modelValue': [value: TreatmentProjectData];
}>();

const { Option } = Select;

// 本地状态
const tableData = ref<TreatmentProjectItem[]>(
  props.modelValue?.tableData || [],
);
const prescription = ref(props.modelValue?.prescription || '');

watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue) {
      tableData.value = newValue.tableData || [];
      prescription.value = newValue.prescription || '';
    }
  },
  { deep: true },
);

const currentData = computed(
  (): TreatmentProjectData => ({
    tableData: tableData.value,
    prescription: prescription.value,
  }),
);

const emitChange = () => {
  const data = currentData.value;
  emit('update:modelValue', data);
  emit('change', data);
};

watch([tableData, prescription], () => emitChange(), { deep: true });

const editConfig = computed<VxeTablePropTypes.EditConfig>(() => ({
  trigger: 'click',
  mode: 'cell',
  showStatus: true,
  enabled: !props.disabled,
}));

const editRules = ref<VxeTablePropTypes.EditRules>({
  name: [{ required: true, message: '请选择项目', trigger: 'change' }],
  amount: [
    { required: true, message: '请输入数量', trigger: 'blur' },
    {
      validator({ cellValue }) {
        const num = Number(cellValue);
        if (Number.isNaN(num) || num <= 0) {
          return new Error('数量必须大于 0');
        }
      },
      trigger: 'blur',
    },
  ],
});

const tableRef = ref();

const validate = async () => {
  // 没有数据则跳过校验，认为通过
  if (!tableData.value || tableData.value.length === 0) {
    return true;
  }
  try {
    const tableErrMap = await tableRef.value.validate(true);
    if (!validTableEditRules(tableErrMap, '诊疗项目')) {
      return false;
    }
    return true;
  } catch {
    return false;
  }
};

const addRow = () => {
  tableData.value.push({
    id: buildShortUUID(),
    name: '',
    code: '',
    price: 0,
    unit: '',
    amount: 1,
    durationMinutes: 0,
    totalPrice: 0,
  });
};

const removeRow = (row: TreatmentProjectItem) => {
  const index = tableData.value.findIndex((item) => item.id === row.id);
  if (index !== -1) {
    tableData.value.splice(index, 1);
  }
};

// 远程搜索项目（简单内置选择器）
const loading = ref(false);
const keyword = ref('');
const options = ref<TreatmentProjectVO[]>([]);

const fetchTreatmentProjects = async (value: string) => {
  keyword.value = value;
  loading.value = true;
  try {
    const res: any = await treatmentProjectList({
      name: value,
      pageSize: 1000,
    });
    options.value = res?.rows || [];
  } finally {
    loading.value = false;
  }
};
const handleSearch = useDebounceFn(fetchTreatmentProjects, 300);

const handleSelectProject = (
  selectedName: string,
  row: TreatmentProjectItem,
) => {
  const selected = options.value.find((i) => i.name === selectedName);
  if (!selected) {
    return;
  }
  row.id = selected.id;
  row.name = selected.name;
  row.code = selected.code;
  row.price = Number(selected.price) || 0;
  row.unit = selected.unit;
  row.durationMinutes = selected.durationMinutes;
  row.totalPrice = row.price * (row.amount || 0);
};

const handleAmountBlur = (row: TreatmentProjectItem) => {
  row.totalPrice = (row.price || 0) * (Number(row.amount) || 0);
};

const totalPrice = computed(() => {
  return tableData.value
    .reduce((sum, item) => sum + (item.price * (item.amount || 0) || 0), 0)
    .toFixed(2);
});

const itemCount = computed(() => tableData.value.length);

const clearData = () => {
  tableData.value = [];
  prescription.value = '';
};

const setData = (data: Partial<TreatmentProjectData>) => {
  if (data.tableData !== undefined) tableData.value = data.tableData;
  if (data.prescription !== undefined) prescription.value = data.prescription;
};

// 组件挂载时默认加载50条数据
onMounted(() => {
  // 默认加载所有诊疗项目数据（不传name参数或传空字符串）
  fetchTreatmentProjects('');
});
/**
 * 获取字典映射关系
 * @returns 字典映射关系
 */
const getDictMapping = (): Record<string, string> => {
  return {
    unit: QiliDictEnum.QILI_TREATMENT_UNIT,
  };
};

defineExpose({
  addRow,
  clearData,
  setData,
  getData: () => currentData.value,
  validate,
  getDictMapping,
});
</script>

<template>
  <div>
    <vxe-table
      ref="tableRef"
      :data="tableData"
      :edit-config="editConfig"
      :edit-rules="editRules"
      :valid-config="{ message: 'tooltip' }"
      border
      stripe
      size="small"
      align="center"
      :max-height="maxHeight"
    >
      <vxe-column type="seq" width="50" title="序号" />
      <vxe-column
        field="name"
        title="项目名称"
        min-width="160"
        :edit-render="{ autoFocus: true, placeholder: '请点击选择项目...' }"
      >
        <template #edit="{ row }">
          <Select
            v-model:value="row.name"
            show-search
            :filter-option="false"
            :loading="loading"
            placeholder="请输入项目名称搜索"
            class="w-full"
            :disabled="props.disabled"
            @search="handleSearch"
            @select="
              (_, option) => handleSelectProject(option?.value as string, row)
            "
          >
            <template v-for="item in options" :key="item.id">
              <Option :value="item.name">
                <div class="flex w-full items-center justify-between">
                  <span class="text-gray-900">{{ item.name }}</span>
                  <span class="text-xs text-gray-400">{{ item.code }}</span>
                </div>
              </Option>
            </template>
            <template #notFoundContent>
              <Spin v-if="loading" size="small" />
              <span v-else>{{
                keyword ? '未找到相关项目' : '请输入关键词搜索'
              }}</span>
            </template>
          </Select>
        </template>
      </vxe-column>
      <vxe-column field="code" title="项目编码" width="120" />
      <vxe-column field="price" title="单价" width="120">
        <template #default="{ row }">
          <span>￥{{ (row.price || 0).toFixed(2) }}</span>
          <span v-if="row.unit" class="ml-1 text-xs text-gray-400">
            / {{ getDictLabel(QiliDictEnum.QILI_TREATMENT_UNIT, row.unit) }}
          </span>
        </template>
      </vxe-column>
      <vxe-column field="amount" title="数量" width="120" :edit-render="{}">
        <template #edit="{ row }">
          <Input
            v-model:value="row.amount"
            type="number"
            placeholder="请输入数量"
            :min="0"
            size="small"
            class="w-full text-center"
            :disabled="props.disabled"
            @blur="() => handleAmountBlur(row)"
          >
            <template #suffix>
              <span class="ml-1 text-xs text-gray-400">次</span>
            </template>
          </Input>
        </template>
        <template #default="{ row }">
          <span>{{ row.amount }}</span>
          <span class="ml-1 text-xs text-gray-400">次</span>
        </template>
      </vxe-column>
      <vxe-column field="durationMinutes" title="时长" width="100">
        <template #default="{ row }">
          <span>{{ row.durationMinutes || 0 }}</span>
          <span class="ml-1 text-xs text-gray-400">分钟</span>
        </template>
      </vxe-column>
      <vxe-column field="totalPrice" title="小计" width="120">
        <template #default="{ row }">
          <span>￥{{ ((row.price || 0) * (row.amount || 0)).toFixed(2) }}</span>
        </template>
      </vxe-column>
      <vxe-column v-if="!props.disabled" title="操作" width="100" fixed="right">
        <template #default="{ row }">
          <Button
            type="link"
            danger
            size="small"
            class="flex items-center"
            :disabled="props.disabled"
            @click="removeRow(row)"
          >
            <i
              class="icon-[material-symbols-light--delete-outline-rounded] mr-1"
            ></i>
            删除
          </Button>
        </template>
      </vxe-column>
    </vxe-table>

    <div class="mt-2 flex select-none rounded-md bg-gray-100 p-2 text-sm">
      <div class="flex w-full items-center justify-start text-sm">
        <label class="w-[3rem]">医嘱:</label>
        <Input
          v-model:value="prescription"
          :placeholder="disabled ? '医嘱' : '请输入医嘱'"
          allow-clear
          size="small"
          class="ml-[1px] w-[50%]"
          :disabled="props.disabled"
        />
      </div>
    </div>

    <div class="mt-4 flex select-none justify-end text-xs">
      <div class="mr-2 flex text-gray-400">
        <label>项目数量：</label>
        <div class="text-primary">{{ itemCount }}</div>
        <span class="ml-1">种</span>
      </div>
      <div class="flex text-gray-400">
        <label>总价：</label>
        <div class="text-primary">{{ totalPrice }}</div>
        <span class="ml-1">元</span>
      </div>
    </div>
  </div>
</template>

<style scoped>
:deep(.vxe-table) {
  border-radius: 8px;
}
</style>
