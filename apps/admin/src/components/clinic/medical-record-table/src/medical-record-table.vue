<script setup lang="ts">
import type { VxeTableEvents, VxeTablePropTypes } from 'vxe-table';

import type { MedicalFieldVO } from '#/api/clinic/medical/field/model';

import { computed, defineEmits, defineProps, ref, withDefaults } from 'vue';

import { Button, Input, Modal } from 'ant-design-vue';

import { FieldDataSelectModal } from '#/components/clinic/field-data-select-modal';
import { isTrue } from '#/utils/cis-common';

// 扩展MedicalFieldVO类型，添加fieldValue属性
interface ExtendedMedicalFieldVO extends MedicalFieldVO {
  fieldValue?: string;
}

interface Props {
  data: ExtendedMedicalFieldVO[];
  disabled?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false,
});

const emit = defineEmits<{
  deleteRow: [row: ExtendedMedicalFieldVO];
  update: [data: ExtendedMedicalFieldVO[]];
}>();

const editConfig = computed<VxeTablePropTypes.EditConfig>(() => ({
  trigger: 'click',
  mode: 'cell',
  showStatus: true,
  enabled: !props.disabled,
}));
const disableFieldName = ref(false);
const editActivatedEvent: VxeTableEvents.EditActivated<any> = ({ row }) => {
  disableFieldName.value = !row.id.startsWith('new_field');
};
// 行配置，启用拖拽功能
const rowConfig = ref<VxeTablePropTypes.RowConfig>({
  // 开启拖拽功能
  drag: true,
});

// 快速选择弹窗相关状态
const modalVisible = ref<Record<number | string, boolean>>({});
const currentRow = ref<ExtendedMedicalFieldVO | null>(null);
const selectedValues = ref<string[]>([]);

const handleQuickSelect = (row: ExtendedMedicalFieldVO) => {
  currentRow.value = row;
  modalVisible.value[row.id] = true;
  // 初始化已选值
  selectedValues.value = row.fieldValue ? row.fieldValue.split(',') : [];
};

const handleFieldDataConfirm = (values: string[]) => {
  if (currentRow.value) {
    currentRow.value.fieldValue = values.join(',');
  }
};

const showDeleteConfirm = (row: ExtendedMedicalFieldVO) => {
  Modal.confirm({
    title: '确定删除此项吗？',
    content: '删除后将无法恢复',
    okText: '确定',
    okType: 'danger',
    cancelText: '取消',
    onOk() {
      emit('deleteRow', row);
    },
  });
};

// 处理行拖拽结束事件
const handleDragEnd = ({
  newTargetData,
}: {
  newTargetData: ExtendedMedicalFieldVO[];
}) => {
  // 通知父组件数据顺序已更改
  emit('update', newTargetData);
};
</script>

<template>
  <div>
    <vxe-table
      :data="data"
      :show-header="false"
      :edit-config="editConfig"
      :row-config="rowConfig"
      :row-drag-config="{
        icon: 'icon-[tabler--menu-order]',
        trigger: 'cell',
      }"
      border
      stripe
      size="small"
      @row-drag-end="handleDragEnd"
      @edit-activated="editActivatedEvent"
    >
      <vxe-column drag-sort width="40" align="center" />
      <vxe-column
        field="fieldName"
        align="center"
        title="行名称"
        width="140"
        :edit-render="{
          autoFocus: true,
          placeholder: '请输入',
        }"
      >
        <template #default="{ row }">
          <span
            class="text-gray-600"
            :class="{ required: isTrue(row.required) }"
          >
            {{ row.fieldName }}
          </span>
        </template>
        <template #edit="{ row }">
          <Input
            v-if="!disableFieldName"
            v-model:value="row.fieldName"
            placeholder="请输入字段"
            allow-clear
            class="flex-1"
          />
          <span
            v-else
            class="text-gray-600"
            :class="{ required: isTrue(row.required) }"
          >
            {{ row.fieldName }}
          </span>
        </template>
      </vxe-column>
      <vxe-column
        field="fieldValue"
        title="输入框"
        :edit-render="{
          autoFocus: true,
          placeholder: '请在此处输入或快速选择',
        }"
      >
        <template #default="{ row }">
          <span v-if="row.fieldValue" class="ml-2 text-gray-700">
            {{ row.fieldValue }}
          </span>
          <span v-else class="ml-2 text-gray-300">
            {{ `请在此处输入或快速选择${row.fieldName}` }}
          </span>
        </template>
        <template #edit="{ row }">
          <div class="flex items-center">
            <Input
              v-model:value="row.fieldValue"
              allow-clear
              class="flex-1"
              :placeholder="`请在此处输入或快速选择${row.fieldName}`"
            />
            <div class="ml-2 flex items-center">
              <Button
                v-if="disableFieldName"
                type="link"
                size="small"
                class="ml-1 flex items-center p-0"
                @click="handleQuickSelect(row)"
              >
                <i class="icon-[mingcute--finger-tap-line] mr-1"></i>
                快速选择
              </Button>
              <Button
                type="link"
                size="small"
                class="ml-2 flex items-center p-0 text-red-500"
                @click="showDeleteConfirm(row)"
              >
                <i
                  class="icon-[material-symbols--delete-outline-rounded] mr-1"
                ></i>
                删除
              </Button>
            </div>
          </div>
        </template>
      </vxe-column>
    </vxe-table>

    <!-- 快速选择弹窗 -->
    <FieldDataSelectModal
      v-if="currentRow"
      :visible="modalVisible[currentRow.id] || false"
      :current-row="currentRow"
      :selected-values="selectedValues"
      @update:visible="
        (val: boolean) => currentRow && (modalVisible[currentRow.id] = val)
      "
      @confirm="handleFieldDataConfirm"
    />
  </div>
</template>

<style lang="css" scoped>
.required {
  color: #f56c6c;
  @apply font-bold;
}
.required::before {
  content: '*';
  color: #f56c6c;
  margin-right: 4px;
}
</style>
