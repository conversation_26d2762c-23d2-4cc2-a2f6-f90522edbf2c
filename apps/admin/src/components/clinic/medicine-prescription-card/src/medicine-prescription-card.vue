<script setup lang="ts">
import type {
  MedicineCategory,
  MedicinePrescriptionData,
} from '#/components/clinic/medicine-prescription-table';

import { ref } from 'vue';

import { <PERSON><PERSON>, Card, message } from 'ant-design-vue';

import { MedicinePrescriptionTable } from '#/components/clinic/medicine-prescription-table';

interface Props {
  /** 处方数据 */
  modelValue: MedicinePrescriptionData;
  /** 药品类别 */
  category: MedicineCategory;
  /** 卡片标题 */
  title: string;
  /** 卡片图标 */
  icon: string;
  /** 图标颜色类 */
  iconColor?: string;
}

interface Emits {
  /** 更新处方数据 */
  (e: 'update:modelValue', value: MedicinePrescriptionData): void;
  /** 处方数据变化 */
  (e: 'change', value: MedicinePrescriptionData): void;
}

const props = withDefaults(defineProps<Props>(), {
  iconColor: 'text-primary',
});
const emit = defineEmits<Emits>();

// 定义 ref
const prescriptionTable = ref();

// 添加一行
const handleAddRow = () => {
  prescriptionTable.value?.addRow();
};

// 清空处方
const handleClear = () => {
  prescriptionTable.value?.clearData();
  message.success(`已清空${props.title}数据`);
};

// 处理数据变化
const handleChange = (data: MedicinePrescriptionData) => {
  emit('update:modelValue', data);
  emit('change', data);
};
// 暴露方法给父组件
defineExpose({
  prescriptionTable,
  validate: () => prescriptionTable.value.validate(),
  getDictMapping: () => prescriptionTable.value.getDictMapping(),
});
</script>

<template>
  <Card size="small">
    <template #title>
      <div class="flex items-center gap-1 text-base font-bold">
        <span :class="[icon, iconColor]"></span>
        <span>{{ title }}</span>
      </div>
    </template>
    <template #extra>
      <div class="flex items-center gap-2">
        <!-- <Button
          type="link"
          size="small"
          class="flex items-center"
          @click="handleApplyTemplate"
        >
          <i class="icon-[tabler--template]"></i>
          选择模板
        </Button> -->
        <Button
          type="link"
          size="small"
          class="flex items-center"
          @click="handleClear"
        >
          <i class="icon-[tabler--trash]"></i>
          清空
        </Button>
        <Button
          type="link"
          size="small"
          class="flex items-center"
          @click="handleAddRow"
        >
          <i class="icon-[tabler--plus]"></i>
          添加一行
        </Button>
      </div>
    </template>
    <MedicinePrescriptionTable
      ref="prescriptionTable"
      :model-value="modelValue"
      :category="category"
      @change="handleChange"
      @update:model-value="$emit('update:modelValue', $event)"
    />
  </Card>
</template>
