<script setup lang="ts">
import type { PaymentForm } from '#/api/clinic/payment/payment/model';

import { computed, reactive, ref, watch } from 'vue';

import {
  Button,
  Card,
  Col,
  Divider,
  Input,
  InputNumber,
  message,
  Modal,
  Radio,
  RadioGroup,
  Row,
  Space,
  Tooltip,
  Typography,
} from 'ant-design-vue';

import { CisTitle } from '../../cis-title';

const props = withDefaults(defineProps<Props>(), {
  paymentItems: () => [],
});

const emit = defineEmits<{
  cancel: [];
  confirm: [paymentData: PaymentData];
  'update:open': [open: boolean];
}>();

const { Title, Text } = Typography;

interface PaymentMethod {
  value: string;
  label: string;
  icon: string;
  color: string;
}

interface PaymentData {
  totalAmount: number;
  paymentMethod: string;
  paidAmount: number;
}

interface Props {
  open: boolean;
  preview?: boolean;
  paymentItems?: {
    name: string;
    price: number;
  }[];
}

// 支付方式选项
const paymentMethods: PaymentMethod[] = [
  {
    value: 'alipay',
    label: '支付宝',
    icon: 'icon-[uiw--alipay]',
    color: '#1677FF',
  },
  {
    value: 'wechat',
    label: '微信支付',
    icon: 'icon-[tdesign--logo-wechatpay-filled]',
    color: '#07C160',
  },
  {
    value: 'cash',
    label: '现金支付',
    icon: 'icon-[ri--money-cny-circle-line]',
    color: '#FF6B35',
  },
];

// 表单数据
const formData = reactive<PaymentData>({
  totalAmount: 0,
  paymentMethod: 'wechat',
  paidAmount: 0,
});

// 编辑状态
const isEditing = ref(false);
const editAmount = ref('');

// 监听 props 变化
watch(
  () => props.paymentItems,
  (newPaymentItems) => {
    // 确保所有费用都是有效数值
    const validItems = newPaymentItems.filter(
      (item) => Number.isFinite(item.price) && item.price >= 0,
    );

    const totalAmount = validItems.reduce(
      (sum, item) => sum + (Number(item.price) || 0),
      0,
    );

    formData.totalAmount = totalAmount;
    formData.paidAmount = totalAmount;
  },
  { immediate: true },
);

// 计算属性
const modalVisible = computed({
  get: () => props.open,
  set: (val) => emit('update:open', val),
});

const selectedPaymentMethod = computed(() => {
  return paymentMethods.find(
    (method) => method.value === formData.paymentMethod,
  );
});

const discountAmount = computed(() => {
  return formData.totalAmount - formData.paidAmount;
});

const discountPercentage = computed(() => {
  if (formData.totalAmount === 0) return 0;
  return ((discountAmount.value / formData.totalAmount) * 100).toFixed(1);
});

// 快速操作函数
const quickActions = [
  {
    label: '取整',
    action: () => Math.floor(formData.paidAmount),
    description: '向下取整到整数',
  },
  {
    label: '抹零',
    action: () => Math.floor(formData.paidAmount),
    description: '去掉小数部分',
  },
];

// 自定义打折相关
const showDiscountInput = ref(false);
const discountInput = ref('');

const handleCustomDiscount = (discount?: number) => {
  discount = discount || Number(discountInput.value);
  if (Number.isNaN(discount) || discount < 0 || discount > 100) {
    message.error('请输入0-100之间的数字');
    return;
  }
  const discountRate = discount / 100;
  formData.paidAmount = Number(
    (formData.totalAmount * discountRate).toFixed(2),
  );
  showDiscountInput.value = false;
  discountInput.value = '';
};

// 事件处理
const handleCancel = () => {
  emit('cancel');
  emit('update:open', false);
  resetForm();
};

const handleConfirm = () => {
  if (formData.paidAmount < 0) {
    message.error('实收金额不能为负数');
    return;
  }

  if (formData.paidAmount > formData.totalAmount * 2) {
    message.error('实收金额异常，请检查');
    return;
  }

  emit('confirm', { ...formData });
  emit('update:open', false);
  resetForm();
};

const resetForm = () => {
  formData.totalAmount = 0;
  formData.paidAmount = 0;
  formData.paymentMethod = 'wechat';
  isEditing.value = false;
  editAmount.value = '';
  showDiscountInput.value = false;
  discountInput.value = '';
};

// 价格编辑相关
const startEdit = () => {
  isEditing.value = true;
  editAmount.value = formData.paidAmount.toString();
};

const cancelEdit = () => {
  isEditing.value = false;
  editAmount.value = '';
};

const confirmEdit = () => {
  const amount = Number(editAmount.value);
  if (Number.isNaN(amount)) {
    message.error('请输入有效的金额');
    return;
  }
  formData.paidAmount = amount;
  isEditing.value = false;
  editAmount.value = '';
};

const handleQuickAction = (actionFn: () => number) => {
  formData.paidAmount = actionFn();
};

// 重置到原价
const resetToOriginal = () => {
  formData.paidAmount = formData.totalAmount;
};

const getPaymentData = (): PaymentForm => {
  return {
    ...formData,
  };
};
// 暴露方法
defineExpose({
  getPaymentData,
  resetForm,
});
</script>

<template>
  <Modal
    v-model:open="modalVisible"
    :title="preview ? '费用明细' : '收费结算'"
    width="600px"
    :mask-closable="false"
    centered
    @cancel="handleCancel"
  >
    <Card
      v-if="preview"
      class="mb-4"
      :bordered="false"
      style="background: #fafafa"
    >
      <div class="text-center">
        <Title :level="4" class="mb-2">费用明细</Title>
        <div class="mb-2 flex items-center justify-between">
          <Text class="font-bold text-gray-600">价格:</Text>
          <Text class="text-lg font-bold">
            ￥{{ formData.totalAmount.toFixed(2) }}
          </Text>
        </div>
        <div
          v-for="item in paymentItems"
          :key="item.name"
          class="flex items-center justify-between text-xs text-gray-400"
        >
          <div>{{ item.name }}</div>
          <div>￥{{ item.price.toFixed(2) }}</div>
        </div>
      </div>
    </Card>
    <div v-else class="payment-modal-content">
      <!-- 费用信息 -->
      <Card class="mb-4" :bordered="false" style="background: #fafafa">
        <div class="text-center">
          <Title :level="4" class="mb-2">费用明细</Title>
          <div class="mb-2 flex items-center justify-between">
            <Text class="font-bold text-gray-600">原价:</Text>
            <Text class="text-lg">￥{{ formData.totalAmount.toFixed(2) }}</Text>
          </div>
          <div
            v-for="item in paymentItems"
            :key="item.name"
            class="flex items-center justify-between text-xs text-gray-400"
          >
            <div>{{ item.name }}</div>
            <div>￥{{ item.price.toFixed(2) }}</div>
          </div>

          <Divider />

          <div class="flex items-center justify-between">
            <Text strong class="text-gray-800">实收:</Text>
            <div class="flex items-center gap-2">
              <template v-if="!isEditing">
                <Title :level="3" class="mb-0 text-red-500">
                  ￥{{ formData.paidAmount.toFixed(2) }}
                </Title>
                <Button type="link" size="small" @click="startEdit">
                  <i class="icon-[material-symbols--edit-outline] text-sm"></i>
                </Button>
              </template>
              <template v-else>
                <div class="flex items-center gap-1">
                  <InputNumber
                    v-model:value="editAmount"
                    style="width: 160px"
                    allow-clear
                    :min="0"
                    :precision="2"
                    placeholder="请输入金额"
                    @press-enter="confirmEdit"
                  >
                    <template #addonBefore>￥</template>
                  </InputNumber>
                  <Button type="link" size="small" @click="confirmEdit">
                    <i
                      class="icon-[material-symbols--check] text-green-500"
                    ></i>
                  </Button>
                  <Button type="link" size="small" @click="cancelEdit">
                    <i class="icon-[material-symbols--close] text-red-500"></i>
                  </Button>
                </div>
              </template>
            </div>
          </div>

          <!-- 优惠信息 -->
          <div class="mt-2 rounded bg-green-200 p-2">
            <Text
              v-if="discountAmount < 0"
              class="flex items-center justify-center text-orange-600"
            >
              <i class="icon-[material-symbols--trending-up] mr-1"></i>
              加收 ￥{{ Math.abs(discountAmount).toFixed(2) }}
            </Text>
            <Text
              v-else
              class="flex items-center justify-center text-green-600"
            >
              <i class="icon-[material-symbols--trending-down] mr-1"></i>
              优惠 ￥{{ discountAmount.toFixed(2) }} ({{ discountPercentage }}%)
            </Text>
          </div>
        </div>
      </Card>
      <!-- 快速编辑 -->
      <Card class="mb-4" size="small">
        <template #title>
          <CisTitle title="快速调整" size="base" />
        </template>
        <template #extra>
          <Tooltip title="恢复原价">
            <Button block size="small" type="primary" @click="resetToOriginal">
              恢复原价
            </Button>
          </Tooltip>
        </template>
        <Row :gutter="[8, 8]">
          <Col v-for="action in quickActions" :key="action.label" :span="8">
            <Tooltip :title="action.description">
              <Button
                block
                size="small"
                @click="handleQuickAction(action.action)"
                :title="action.description"
              >
                {{ action.label }}
              </Button>
            </Tooltip>
          </Col>

          <Col :span="8">
            <Tooltip title="99折">
              <Button block size="small" @click="handleCustomDiscount(99)">
                99折
              </Button>
            </Tooltip>
          </Col>
          <Col :span="8">
            <Tooltip title="88折">
              <Button block size="small" @click="handleCustomDiscount(88)">
                88折
              </Button>
            </Tooltip>
          </Col>
          <Col :span="8">
            <Tooltip title="85折">
              <Button block size="small" @click="handleCustomDiscount(85)">
                85折
              </Button>
            </Tooltip>
          </Col>

          <Col :span="8">
            <Tooltip title="自定义折扣">
              <Button
                block
                size="small"
                @click="showDiscountInput = true"
                title="自定义折扣"
              >
                自定义折扣
              </Button>
            </Tooltip>
          </Col>
        </Row>

        <!-- 自定义折扣输入 -->
        <div v-show="showDiscountInput" class="mt-3 flex items-center gap-2">
          <Input
            v-model:value="discountInput"
            placeholder="8折 输入80"
            style="width: 200px"
            size="small"
            type="number"
            :min="0"
            @press-enter="handleCustomDiscount(Number(discountInput))"
          >
            <template #suffix>
              <span>%</span>
            </template>
          </Input>
          <Button
            size="small"
            type="primary"
            @click="handleCustomDiscount(Number(discountInput))"
          >
            确定
          </Button>
          <Button size="small" @click="showDiscountInput = false">
            取消
          </Button>
        </div>
      </Card>

      <!-- 支付方式选择 -->
      <Card size="small">
        <template #title>
          <CisTitle title="支付方式" size="base" />
        </template>
        <RadioGroup v-model:value="formData.paymentMethod" class="w-full">
          <Row :gutter="[16, 16]">
            <Col v-for="method in paymentMethods" :key="method.value" :span="8">
              <div class="payment-method-card">
                <Radio :value="method.value" class="hidden">
                  {{ method.label }}
                </Radio>
                <Card
                  size="small"
                  class="payment-method-option cursor-pointer border-2 transition-all"
                  :class="[
                    formData.paymentMethod === method.value
                      ? 'border-2 border-blue-500 bg-blue-200'
                      : 'border-gray-200 hover:border-blue-300',
                  ]"
                  @click="formData.paymentMethod = method.value"
                >
                  <div class="text-center">
                    <div class="mb-2">
                      <i
                        :class="method.icon"
                        class="text-2xl"
                        :style="{ color: method.color }"
                      ></i>
                    </div>
                    <Text class="text-sm">{{ method.label }}</Text>
                  </div>
                </Card>
              </div>
            </Col>
          </Row>
        </RadioGroup>
      </Card>
    </div>

    <template #footer>
      <div v-if="!preview" class="flex items-center justify-between">
        <div class="text-left">
          <Text class="text-gray-500">支付方式: </Text>
          <Text strong :style="{ color: selectedPaymentMethod?.color }">
            {{ selectedPaymentMethod?.label }}
          </Text>
        </div>
        <Space>
          <Button @click="handleCancel">取消</Button>
          <Button type="primary" @click="handleConfirm">
            确认收费 ￥{{ formData.paidAmount.toFixed(2) }}
          </Button>
        </Space>
      </div>
    </template>
  </Modal>
</template>

<style scoped>
.payment-modal-content {
  max-height: 70vh;
  overflow-y: auto;
}

.payment-method-option {
  transition: all 0.3s ease;
}

.payment-method-option:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
</style>
