<script setup lang="ts">
import type { MedicalRecordVO } from '#/api/clinic/medical/record/model';
import type { PatientVO } from '#/api/clinic/patient/model';
import type { ID } from '#/api/common';

import { computed, onMounted, onUnmounted, ref, watch } from 'vue';

import {
  Button,
  Card,
  Dropdown,
  Empty,
  Modal,
  Spin,
  Tag,
} from 'ant-design-vue';
import { debounce } from 'lodash-es';

import { medicalRecordList } from '#/api/clinic/medical/record';
import { CisTitle } from '#/components/clinic/cis-title';
import { MedicalRecordTable } from '#/components/clinic/medical-record-table';
import { MedicinePrescriptionTable } from '#/components/clinic/medicine-prescription-table';
import { PrescriptionPrintMenu } from '#/components/clinic/prescription-print-menu';
import { TCMPrescriptionTable } from '#/components/clinic/tcm-prescription-table';
import { TreatmentProjectTable } from '#/components/clinic/treatment-project-table';
import {
  MedicalRecordStatus,
  MedicineCategory,
  PrintType,
} from '#/constants/qili-common-enum';
import { getStatusTag } from '#/utils/patient';

interface Props {
  // 移除 records 属性，改为通过 patientId 加载数据
  patientId?: ID | null;
  currentRecordId?: null | number | string;
  // 新增：填充功能配置
  enableAutofill?: boolean;
  // 新增：当前患者ID（用于权限控制）
  currentPatientId?: null | number | string;
  // 新增：是否正在接诊
  isAttending?: boolean;
  currentPatient: PatientVO;
}

interface Emits {
  // 新增：数据加载完成事件
  recordsLoaded: [
    records: MedicalRecordVO[],
    currentRecord: MedicalRecordVO | null,
  ];
  // 新增：一键填充事件
  autofillAll: [record: MedicalRecordVO];
  // 新增：选择性填充事件
  autofillDiagnosis: [record: MedicalRecordVO];
  autofillTcm: [record: MedicalRecordVO];
  autofillChineseMedicine: [record: MedicalRecordVO];
  autofillWesternMedicine: [record: MedicalRecordVO];
  autofillTreatment: [record: MedicalRecordVO];
}

const props = withDefaults(defineProps<Props>(), {
  patientId: null,
  currentRecordId: null,
  currentPatientId: null,
  enableAutofill: true,
  isAttending: false,
});

const emit = defineEmits<Emits>();

// 内部状态管理
const records = ref<MedicalRecordVO[]>([]);
const currentRecord = ref<MedicalRecordVO | null>(null);
const internalLoading = ref(false);
// 当前正在填充的操作
const currentFillOperation = ref<null | string>(null);

// 新增：查看完整药品列表弹窗状态
const showMedicineListModal = ref(false);
const currentMedicineData = ref<any>(null);
const currentMedicineType = ref<string>('');
const currentMedicineRecord = ref<MedicalRecordVO | null>(null);
const currentMedicineDataKey = ref<string>('');

// 计算是否可以使用填充功能 - 简化逻辑，只要启用填充功能就允许
const canAutofill = computed(() => {
  return props.enableAutofill;
});

// 最近一次操作标签

// 获取数据预览文本
const getPreviewText = (jsonStr: string, maxLength = 20): string => {
  if (!jsonStr || jsonStr.trim() === '') {
    return '无数据';
  }

  const data = JSON.parse(jsonStr);
  if (data && typeof data === 'object') {
    // 尝试提取有意义的预览信息
    if (data.fieldList && Array.isArray(data.fieldList)) {
      // 病历诊断预览
      const mainComplaint = data.fieldList.find(
        (field: any) => field.fieldName === '主诉' && field.fieldValue,
      );
      if (mainComplaint) {
        return mainComplaint.fieldValue.length > maxLength
          ? `${mainComplaint.fieldValue.slice(0, Math.max(0, maxLength))}...`
          : mainComplaint.fieldValue;
      }
    } else if (data.tableData && Array.isArray(data.tableData)) {
      // 处方预览
      const itemCount = data.tableData.length;
      if (itemCount === 0) {
        return '无数据';
      }
      if (itemCount > 0) {
        const firstItem = data.tableData[0];
        return `${firstItem.name || ''}等${itemCount}项`;
      }
    } else if (data.prescription) {
      // 医嘱预览
      return data.prescription.length > maxLength
        ? `${data.prescription.slice(0, Math.max(0, maxLength))}...`
        : data.prescription;
    }
  }
  return '有数据';
};

// 检查是否有数据
const hasData = (jsonStr: string): boolean => {
  if (!jsonStr || jsonStr.trim() === '') {
    return false;
  }

  try {
    const data = JSON.parse(jsonStr);
    if (data && typeof data === 'object') {
      if (data.fieldList && Array.isArray(data.fieldList)) {
        return data.fieldList.some((field: any) => field.fieldValue);
      } else if (data.tableData && Array.isArray(data.tableData)) {
        return data.tableData.length > 0;
      } else if (data.prescription) {
        return data.prescription.trim() !== '';
      }
    }
    return false;
  } catch {
    return false;
  }
};

// 一键填充处理（添加防抖）
const handleAutofillAll = debounce((record: MedicalRecordVO, event: Event) => {
  event.stopPropagation();
  if (!canAutofill.value) return;

  currentFillOperation.value = `all-${record.id}`;
  emit('autofillAll', record);
}, 300);

// 选择性填充处理（添加防抖）
const handleAutofillDiagnosis = debounce(
  (record: MedicalRecordVO, event: Event) => {
    event.stopPropagation();
    if (!canAutofill.value) return;

    currentFillOperation.value = `diagnosis-${record.id}`;
    emit('autofillDiagnosis', record);
  },
  300,
);

const handleAutofillTcm = debounce((record: MedicalRecordVO, event: Event) => {
  event.stopPropagation();
  if (!canAutofill.value) return;

  currentFillOperation.value = `tcm-${record.id}`;
  emit('autofillTcm', record);
}, 300);

const handleAutofillChineseMedicine = debounce(
  (record: MedicalRecordVO, event: Event) => {
    event.stopPropagation();
    if (!canAutofill.value) return;

    currentFillOperation.value = `chinese-medicine-${record.id}`;
    emit('autofillChineseMedicine', record);
  },
  300,
);

const handleAutofillWesternMedicine = debounce(
  (record: MedicalRecordVO, event: Event) => {
    event.stopPropagation();
    if (!canAutofill.value) return;

    currentFillOperation.value = `western-medicine-${record.id}`;
    emit('autofillWesternMedicine', record);
  },
  300,
);

const handleAutofillTreatment = debounce(
  (record: MedicalRecordVO, event: Event) => {
    event.stopPropagation();
    if (!canAutofill.value) return;

    currentFillOperation.value = `treatment-${record.id}`;
    emit('autofillTreatment', record);
  },
  300,
);

// 新增：查看完整药品列表
const showMedicineList = (
  record: MedicalRecordVO,
  dataKey: string,
  event: Event,
) => {
  event.stopPropagation();

  try {
    const jsonData = record[dataKey as keyof MedicalRecordVO] as string;
    if (!jsonData || jsonData.trim() === '') {
      return;
    }

    const data = JSON.parse(jsonData);
    if (data && typeof data === 'object') {
      currentMedicineData.value = data;
      currentMedicineRecord.value = record;
      currentMedicineDataKey.value = dataKey;

      // 根据数据类型设置显示类型
      switch (dataKey) {
        case 'diagnosisJson': {
          currentMedicineType.value = '诊断信息';
          break;
        }
        case 'tcmpPrescriptionJson': {
          currentMedicineType.value = '中成药处方';
          break;
        }
        case 'tcmPrescriptionJson': {
          currentMedicineType.value = '中药处方';
          break;
        }
        case 'treatmentItemJson': {
          currentMedicineType.value = '诊疗项目';
          break;
        }
        case 'wmPrescriptionJson': {
          currentMedicineType.value = '西药处方';
          break;
        }
        default: {
          currentMedicineType.value = '详细信息';
          break;
        }
      }

      showMedicineListModal.value = true;
    }
  } catch (error) {
    console.error('解析药品数据失败:', error);
  }
};

// 处方信息配置类型定义
interface PrescriptionConfig {
  key: string;
  label: string;
  bgColor: string;
  handler: (record: MedicalRecordVO, event: Event) => void;
  dataKey: keyof Pick<
    MedicalRecordVO,
    | 'diagnosisJson'
    | 'tcmpPrescriptionJson'
    | 'tcmPrescriptionJson'
    | 'treatmentItemJson'
    | 'wmPrescriptionJson'
  >;
}

// 处方信息配置数组
const prescriptionConfigs: PrescriptionConfig[] = [
  {
    key: 'diagnosis',
    label: '诊断',
    bgColor: 'bg-gray-50',
    handler: handleAutofillDiagnosis,
    dataKey: 'diagnosisJson',
  },
  {
    key: 'tcm',
    label: '中药处方',
    bgColor: 'bg-green-50',
    handler: handleAutofillTcm,
    dataKey: 'tcmPrescriptionJson',
  },
  {
    key: 'tcmp',
    label: '中成药处方',
    bgColor: 'bg-blue-50',
    handler: handleAutofillChineseMedicine,
    dataKey: 'tcmpPrescriptionJson',
  },
  {
    key: 'wm',
    label: '西药处方',
    bgColor: 'bg-red-50',
    handler: handleAutofillWesternMedicine,
    dataKey: 'wmPrescriptionJson',
  },
  {
    key: 'treatment',
    label: '诊疗项目',
    bgColor: 'bg-purple-50',
    handler: handleAutofillTreatment,
    dataKey: 'treatmentItemJson',
  },
];

// 新增：为每个配置添加查看方法
const getViewHandler = (dataKey: string) => {
  return (record: MedicalRecordVO, event: Event) => {
    showMedicineList(record, dataKey, event);
  };
};

// 新增：弹窗中的填充操作
const handleModalAutofill = () => {
  if (!currentMedicineRecord.value || !currentMedicineDataKey.value) {
    return;
  }

  const record = currentMedicineRecord.value;
  const dataKey = currentMedicineDataKey.value;

  // 根据数据类型调用对应的填充方法
  const mockEvent = new Event('click');
  switch (dataKey) {
    case 'diagnosisJson': {
      handleAutofillDiagnosis(record, mockEvent);
      break;
    }
    case 'tcmpPrescriptionJson': {
      handleAutofillChineseMedicine(record, mockEvent);
      break;
    }
    case 'tcmPrescriptionJson': {
      handleAutofillTcm(record, mockEvent);
      break;
    }
    case 'treatmentItemJson': {
      handleAutofillTreatment(record, mockEvent);
      break;
    }
    case 'wmPrescriptionJson': {
      handleAutofillWesternMedicine(record, mockEvent);
      break;
    }
  }

  // 填充后关闭弹窗
  showMedicineListModal.value = false;
};

// 检查是否正在加载特定操作
const isLoading = (operation: string, recordId: number | string): boolean => {
  return (
    internalLoading.value &&
    currentFillOperation.value === `${operation}-${recordId}`
  );
};

// 新增：获取当前弹窗对应的配置
const currentModalConfig = computed(() => {
  if (!currentMedicineDataKey.value) return null;

  return prescriptionConfigs.find(
    (config) => config.dataKey === currentMedicineDataKey.value,
  );
});

// 新增：检查当前弹窗是否正在加载
const isModalLoading = computed(() => {
  if (!currentMedicineRecord.value || !currentModalConfig.value) {
    return false;
  }

  return isLoading(
    currentModalConfig.value.key,
    currentMedicineRecord.value.id,
  );
});

// 清除加载状态
const clearLoadingState = () => {
  currentFillOperation.value = null;
};

// 构建打印数据
const buildPrintData = (printType: PrintType, record?: MedicalRecordVO) => {
  if (!record) {
    console.error('记录为空，无法打印');
    return null;
  }
  // 构建病历模板信息
  const medicalField = {
    id: '',
    name: '',
    type: '',
    directory: '',
    description: '',
    fieldList: [],
  };

  // 解析处方数据
  let tcmPrescriptionData;
  let zcyPrescriptionData;
  let xyPrescriptionData;

  try {
    if (record.tcmPrescriptionJson) {
      tcmPrescriptionData = JSON.parse(record.tcmPrescriptionJson);
    }
    if (record.tcmpPrescriptionJson) {
      zcyPrescriptionData = JSON.parse(record.tcmpPrescriptionJson);
    }
    if (record.wmPrescriptionJson) {
      xyPrescriptionData = JSON.parse(record.wmPrescriptionJson);
    }
  } catch (error) {
    console.error('解析处方数据失败:', error);
  }

  // 返回打印数据
  return {
    printType,
    patientId: record.patientId,
    medicalField,
    visitTime: record.visitTime,
    visitNo: record.visitNo,
    totalAmount: 0,
    tcmPrescriptionData,
    zcyPrescriptionData,
    xyPrescriptionData,
  };
};

// 加载患者就诊历史记录
const loadMedicalRecordHistory = async (patientId: ID) => {
  if (!patientId) return;

  internalLoading.value = true;
  try {
    const queryParams = {
      patientId,
      pageNum: 1,
      pageSize: 100,
    };
    const res = await medicalRecordList(queryParams);
    records.value = res.rows.reverse();

    // 如果有就诊中，则加载就诊中的诊号
    const inProgressRecord = res.rows.find(
      (item) => item.status === MedicalRecordStatus.IN_PROGRESS,
    );
    currentRecord.value = inProgressRecord || null;

    // 通知父组件数据加载完成
    emit('recordsLoaded', records.value, currentRecord.value);
  } catch (error) {
    console.error('加载就诊历史失败:', error);
    records.value = [];
    currentRecord.value = null;
  } finally {
    internalLoading.value = false;
  }
};

// 监听 patientId 变化，自动加载数据
watch(
  () => props.patientId,
  async (newPatientId) => {
    if (newPatientId) {
      // 当patientId有值时，总是重新加载数据
      await loadMedicalRecordHistory(newPatientId);
    } else {
      // 当patientId为空时，清空数据
      records.value = [];
      currentRecord.value = null;
    }
  },
  { immediate: true },
);

// 手动刷新数据
const refreshData = async () => {
  if (props.patientId) {
    await loadMedicalRecordHistory(props.patientId);
  }
};

// 键盘快捷键支持
const handleKeydown = (event: KeyboardEvent) => {
  // 只在可以填充且有记录时处理快捷键
  if (!canAutofill.value || !records.value || records.value.length === 0) {
    return;
  }

  // Ctrl/Cmd + Shift + A: 填充第一条记录的所有数据
  if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'A') {
    event.preventDefault();
    const firstRecord = records.value[0];
    if (firstRecord) {
      handleAutofillAll(firstRecord, event as any);
    }
  }
};

// 添加和移除键盘事件监听
onMounted(async () => {
  document.addEventListener('keydown', handleKeydown);

  // 确保首次加载时如果有patientId就加载数据
  if (props.patientId && records.value.length === 0) {
    await loadMedicalRecordHistory(props.patientId);
  }
});

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown);
});

// 暴露方法给父组件
defineExpose({
  clearLoadingState,
  refreshData,
});
</script>

<template>
  <Spin :spinning="internalLoading">
    <Empty v-if="!records || records.length === 0" class="mt-[90%]" />
    <Card v-for="record in records" :key="record.id" size="small" class="mt-4">
      <template #title>
        <div class="flex w-full items-center justify-between pr-8">
          <span
            class="text-sm"
            :class="{
              'text-primary':
                record.id === currentRecordId &&
                record.status === MedicalRecordStatus.IN_PROGRESS,
            }"
          >
            诊号：{{ record.visitNo }}
          </span>
        </div>
      </template>
      <template #extra>
        <div class="flex items-center gap-1">
          <Tag
            size="small"
            :color="getStatusTag(record.status).color"
            class="flex items-center gap-1"
          >
            <i :class="getStatusTag(record.status).icon"></i>
            {{ getStatusTag(record.status).text }}
          </Tag>
        </div>
      </template>

      <div class="space-y-3">
        <div
          v-if="canAutofill"
          class="border-1 flex items-center justify-end gap-1 border-b pb-2"
        >
          <Button
            size="small"
            type="primary"
            :loading="isLoading('all', record.id)"
            :disabled="
              !hasData(record.diagnosisJson) &&
              !hasData(record.tcmPrescriptionJson) &&
              !hasData(record.tcmpPrescriptionJson) &&
              !hasData(record.wmPrescriptionJson) &&
              !hasData(record.treatmentItemJson)
            "
            @click="handleAutofillAll(record, $event)"
            class="flex items-center gap-1"
          >
            <i class="icon-[material-symbols--auto-fix-high]"></i>
            一键填充
          </Button>
          <Dropdown
            type="primary"
            size="small"
            trigger="hover"
            class="flex items-center"
          >
            <template #overlay>
              <PrescriptionPrintMenu
                :current-patient="currentPatient"
                :record="record"
                :is-history-mode="true"
                :print-data="buildPrintData"
              />
            </template>
            <Button type="primary" size="small" class="flex items-center gap-1">
              <i class="icon-[material-symbols--print]"></i>
              打印
              <i class="icon-[icon-park-outline--down]"></i>
            </Button>
          </Dropdown>
        </div>
        <!-- 使用循环渲染处方信息 -->
        <div
          v-for="config in prescriptionConfigs"
          :key="config.key"
          :class="`flex items-center justify-between rounded ${config.bgColor} p-2`"
        >
          <div class="flex flex-1 items-center text-sm">
            <span class="select-none font-medium text-gray-700">
              {{ config.label }}：
            </span>
            <span
              class="text-gray-600"
              :class="{
                'hover:text-primary cursor-pointer underline':
                  canAutofill && hasData(record[config.dataKey]),
              }"
              @click="
                canAutofill && hasData(record[config.dataKey])
                  ? getViewHandler(config.dataKey)(record, $event)
                  : null
              "
            >
              {{ getPreviewText(record[config.dataKey]) }}
            </span>
          </div>
          <Button
            v-if="canAutofill"
            size="small"
            type="link"
            :loading="isLoading(config.key, record.id)"
            :disabled="!hasData(record[config.dataKey])"
            class="flex items-center"
            @click="config.handler(record, $event)"
          >
            <i class="icon-[material-symbols--content-copy]"></i>
            填充
          </Button>
        </div>

        <!-- 其他信息 -->
        <div class="mt-4 border-t border-gray-200 pt-3">
          <div class="flex flex-col gap-1 text-sm text-gray-500">
            <span>接诊医生：{{ record.doctorName }}</span>
            <span>
              <span> 接诊时间：</span>
              <span class="">
                {{ record.visitTime || '--' }}
              </span>
            </span>
          </div>
        </div>
      </div>
    </Card>
  </Spin>

  <!-- 新增：查看完整药品列表弹窗 -->
  <Modal
    v-model:open="showMedicineListModal"
    :title="`查看完整${currentMedicineType}`"
    width="60%"
    @cancel="showMedicineListModal = false"
  >
    <template #footer>
      <div class="flex justify-end gap-2">
        <Button @click="showMedicineListModal = false"> 关闭</Button>
        <Button
          v-if="canAutofill && currentMedicineRecord"
          type="primary"
          :loading="isModalLoading"
          @click="handleModalAutofill"
        >
          <i class="icon-[material-symbols--content-copy] mr-1"></i>
          填充
        </Button>
      </div>
    </template>
    <div v-if="currentMedicineData" class="max-h-[600px] overflow-y-auto">
      <CisTitle title="详情表格" class="my-4" />
      <!-- 诊断信息 - 使用 medical-record-table -->
      <div
        v-if="
          currentMedicineData.fieldList &&
          Array.isArray(currentMedicineData.fieldList)
        "
      >
        <MedicalRecordTable
          :data="currentMedicineData.fieldList"
          :disabled="true"
          @delete-row="() => {}"
          @update="() => {}"
        />
      </div>

      <!-- 中药处方 - 使用 tcm-prescription-table -->
      <div
        v-else-if="
          currentMedicineType === '中药处方' &&
          currentMedicineData.tableData &&
          Array.isArray(currentMedicineData.tableData)
        "
      >
        <TCMPrescriptionTable
          :model-value="currentMedicineData"
          :disabled="true"
          max-height="400px"
        />
      </div>

      <!-- 中成药处方 - 使用 medicine-prescription-table -->
      <div
        v-else-if="
          currentMedicineType === '中成药处方' &&
          currentMedicineData.tableData &&
          Array.isArray(currentMedicineData.tableData)
        "
      >
        <MedicinePrescriptionTable
          :model-value="currentMedicineData"
          :category="MedicineCategory.ZCY"
          :disabled="true"
          max-height="400px"
        />
      </div>

      <!-- 西药处方 - 使用 medicine-prescription-table -->
      <div
        v-else-if="
          currentMedicineType === '西药处方' &&
          currentMedicineData.tableData &&
          Array.isArray(currentMedicineData.tableData)
        "
      >
        <MedicinePrescriptionTable
          :model-value="currentMedicineData"
          :category="MedicineCategory.XY"
          :disabled="true"
          max-height="400px"
        />
      </div>

      <!-- 诊疗项目 - 使用 treatment-project-table -->
      <div
        v-else-if="
          currentMedicineType === '诊疗项目' &&
          currentMedicineData.tableData &&
          Array.isArray(currentMedicineData.tableData)
        "
      >
        <TreatmentProjectTable
          :model-value="currentMedicineData"
          :disabled="true"
          max-height="400px"
        />
      </div>

      <!-- 其他格式数据 -->
      <div v-else>
        <pre class="overflow-auto text-sm text-gray-600">
          {{ JSON.stringify(currentMedicineData, null, 2) }}
        </pre>
      </div>
    </div>

    <div v-else class="py-8 text-center text-gray-500">暂无数据</div>
  </Modal>
</template>

<style scoped>
.cursor-pointer {
  cursor: pointer;
}
</style>
