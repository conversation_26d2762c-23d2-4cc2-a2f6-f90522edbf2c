<script setup lang="ts">
import type { DrugQuery, DrugVO } from '#/api/clinic/pharmacy/drug/model';
import type { MedicineCategory } from '#/constants/qili-common-enum';

import { computed, onMounted, ref, watch } from 'vue';

import { useDebounceFn } from '@vueuse/core';
import { Select, SelectOption, Spin } from 'ant-design-vue';

import { drugList } from '#/api/clinic/pharmacy/drug';
import { priceToYuan } from '#/utils/cis-common';

const props = defineProps<{
  category?: MedicineCategory;
  // 支持返回完整药品对象
  drug?: DrugVO;
  // 新增：已选择的药品列表，用于禁用已选择的药品
  selectedDrugs?: DrugVO[];
  value?: string;
}>();

const emit = defineEmits<{
  (e: 'update:value', value: string): void;
  (e: 'update:drug', drug: DrugVO | undefined): void;
  (e: 'select', drug: DrugVO): void;
  (e: 'dropdownVisibleChange', visible: boolean): void;
}>();

const loading = ref(false);
const keyword = ref('');
const options = ref<DrugVO[]>([]);

const modelValue = computed({
  get: () => props.value || undefined,
  set: (value: string) => emit('update:value', value),
});

const queryParams = computed(() => {
  const queryParams: DrugQuery = {
    searchValue: keyword.value,
    pageSize: 1000,
  };
  if (props.category) {
    queryParams.category = props.category;
  }
  return queryParams;
});

const searchDrugs = async (value: string) => {
  keyword.value = value;
  loading.value = true;
  try {
    const res: any = await drugList(queryParams.value);
    options.value = res.rows.map((item: DrugVO) => {
      return {
        ...item,
        price: priceToYuan(item.price),
      };
    });
  } finally {
    loading.value = false;
  }
};

// 使用防抖函数，延迟300ms执行搜索
const handleSearch = useDebounceFn(searchDrugs, 300);

// 处理选择药品
const handleSelect = (value: any) => {
  const selectedDrug = options.value.find((item) => item.name === value);
  if (selectedDrug) {
    emit('update:drug', selectedDrug);
    emit('select', selectedDrug);
  }
};

// 判断药品是否被禁用
const isDrugDisabled = (item: DrugVO) => {
  return props.selectedDrugs?.some(
    (selected) => selected.id === item.id && selected.name !== props.value,
  );
};

// 检查下拉框是否展开
const isDropdownOpen = () => {
  // 检查页面中是否存在可见的 ant-select-dropdown 元素
  const dropdown = document.querySelector('.ant-select-dropdown');
  if (!dropdown) {
    return false;
  }

  // 检查元素是否可见（display 不为 none）
  const computedStyle = window.getComputedStyle(dropdown);
  return computedStyle.display !== 'none';
};

const handleKeydown = (e: KeyboardEvent) => {
  const stopKeys = ['Enter', 'ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'];
  if (stopKeys.includes(e.key)) {
    if (isDropdownOpen()) {
      // 下拉框打开时，阻止事件冒泡，让 Select 组件处理
      e.stopPropagation();
    } else {
      // 下拉框未打开时，让事件冒泡到父组件
      e.preventDefault();
    }
  }
};

// 处理下拉框状态变化
const handleDropdownVisibleChange = (visible: boolean) => {
  emit('dropdownVisibleChange', visible);
};

// 组件挂载时默认加载50条数据
onMounted(() => {
  // 默认加载药品数据（空字符串搜索返回前50条）
  searchDrugs('');
});

// 监听外部value变化，如果有值但options为空，则搜索一次
watch(
  () => props.value,
  (value) => {
    if (value && options.value.length === 0) {
      // 初始化时直接调用搜索，不需要防抖
      searchDrugs(value);
    }
  },
  { immediate: true },
);
</script>

<template>
  <Select
    v-model:value="modelValue"
    show-search
    allow-clear
    :filter-option="false"
    :loading="loading"
    placeholder="请输入药品名称搜索"
    virtual
    :default-active-first-option="false"
    @search="handleSearch"
    @select="handleSelect"
    @keydown="handleKeydown"
    @dropdown-visible-change="handleDropdownVisibleChange"
  >
    <template v-for="item in options" :key="item.id">
      <SelectOption :value="item.name" :disabled="isDrugDisabled(item)">
        <div class="flex w-full items-center justify-between">
          <span
            class="font-medium"
            :class="{
              'text-gray-400': isDrugDisabled(item),
              'text-gray-900': !isDrugDisabled(item),
            }"
          >
            {{ item.name }}
            <span v-if="isDrugDisabled(item)" class="text-xs">(已选)</span>
          </span>
          <span class="text-xs text-gray-400">{{ item.code }}</span>
        </div>
      </SelectOption>
    </template>

    <template #notFoundContent>
      <Spin v-if="loading" size="small" />
      <span v-else>{{ keyword ? '未找到相关药品' : '请输入关键词搜索' }}</span>
    </template>
  </Select>
</template>

<style scoped>
:deep(.ant-select) {
  width: 100%;
}
</style>
