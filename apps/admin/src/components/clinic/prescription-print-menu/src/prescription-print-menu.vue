<script setup lang="ts">
import type { MedicalRecordVO } from '#/api/clinic/medical/record/model';
import type { PatientVO } from '#/api/clinic/patient/model';

import { ref } from 'vue';

import { Button, Dropdown, Menu, MenuItem } from 'ant-design-vue';

import PrescriptionPrint from '#/components/clinic/prescription-print/src/prescription-print.vue';
import { PrintType } from '#/constants/qili-common-enum';

interface Props {
  // 打印可用性状态
  isTcmPrescriptionPrint?: boolean;
  isZcyPrescriptionPrint?: boolean;
  isXyPrescriptionPrint?: boolean;
  // 患者信息（用于权限控制）
  currentPatient?: null | PatientVO;
  // 历史记录模式的额外参数
  record?: MedicalRecordVO;
  // 是否为历史记录模式
  isHistoryMode?: boolean;
  // 打印数据构建函数（由父组件提供）
  printData?: (printType: PrintType, record?: MedicalRecordVO) => any;
}

interface Emits {
  printPrescription: [printType: PrintType, record?: MedicalRecordVO];
}

const props = withDefaults(defineProps<Props>(), {
  isTcmPrescriptionPrint: false,
  isZcyPrescriptionPrint: false,
  isXyPrescriptionPrint: false,
  currentPatient: null,
  record: undefined,
  isHistoryMode: false,
  printData: undefined,
});

const emit = defineEmits<Emits>();

// 打印相关状态
const showPrescriptionPrint = ref(false);
const prescriptionData = ref<any>(null);

// 处理打印事件
const handlePrint = (printType: PrintType) => {
  // 如果提供了构建函数，使用它来构建打印数据
  if (props.printData) {
    prescriptionData.value = props.printData(printType, props.record);
    showPrescriptionPrint.value = true;
  } else {
    // 否则发出事件让父组件处理
    emit('printPrescription', printType, props.record);
  }
};

// 检查是否有数据（历史记录模式使用）
const hasData = (jsonStr: string | undefined): boolean => {
  if (!jsonStr || jsonStr.trim() === '') {
    return false;
  }

  try {
    const data = JSON.parse(jsonStr);
    if (data && typeof data === 'object') {
      if (data.fieldList && Array.isArray(data.fieldList)) {
        return data.fieldList.some((field: any) => field.fieldValue);
      } else if (data.tableData && Array.isArray(data.tableData)) {
        return data.tableData.length > 0;
      } else if (data.prescription) {
        return data.prescription.trim() !== '';
      }
    }
    return false;
  } catch {
    return false;
  }
};

// 计算禁用状态
const getTcmDisabled = () => {
  if (props.isHistoryMode && props.record) {
    return (
      !hasData(props.record.tcmPrescriptionJson) || !props.currentPatient?.id
    );
  }
  return !props.isTcmPrescriptionPrint || !props.currentPatient?.id;
};

const getZcyDisabled = () => {
  if (props.isHistoryMode && props.record) {
    return (
      !hasData(props.record.tcmpPrescriptionJson) || !props.currentPatient?.id
    );
  }
  return !props.isZcyPrescriptionPrint || !props.currentPatient?.id;
};

const getXyDisabled = () => {
  if (props.isHistoryMode && props.record) {
    return (
      !hasData(props.record.wmPrescriptionJson) || !props.currentPatient?.id
    );
  }
  return !props.isXyPrescriptionPrint || !props.currentPatient?.id;
};
</script>

<template>
  <div>
    <!-- 打印按钮和下拉菜单 -->
    <Dropdown
      type="primary"
      size="small"
      trigger="hover"
      class="flex items-center"
    >
      <template #overlay>
        <Menu>
          <MenuItem key="0" :disabled="getTcmDisabled()">
            <a
              href="javascript:;"
              class="flex items-center"
              @click="handlePrint(PrintType.TCM_PRESCRIPTION)"
            >
              <i class="icon-[material-symbols-light--prescriptions]"></i>
              打印中药处方
            </a>
          </MenuItem>
          <MenuItem key="1" :disabled="getZcyDisabled()">
            <a
              href="javascript:;"
              class="flex items-center"
              @click="handlePrint(PrintType.CHINESE_MEDICINE)"
            >
              <i class="icon-[material-symbols-light--prescriptions]"></i>
              打印中成药处方
            </a>
          </MenuItem>
          <MenuItem key="2" :disabled="getXyDisabled()">
            <a
              href="javascript:;"
              class="flex items-center"
              @click="handlePrint(PrintType.WESTERN_MEDICINE)"
            >
              <i class="icon-[material-symbols-light--prescriptions]"></i>
              打印西药处方
            </a>
          </MenuItem>
        </Menu>
      </template>
      <Button type="primary" size="small" class="flex items-center gap-1">
        <i class="icon-[material-symbols--print]"></i>
        打印
        <i class="icon-[icon-park-outline--down]"></i>
      </Button>
    </Dropdown>

    <!-- 使用 Teleport 将打印组件传送到 body，完全隔离 -->
    <Teleport to="body">
      <PrescriptionPrint
        v-show="prescriptionData && showPrescriptionPrint"
        :data="prescriptionData"
        :visible="showPrescriptionPrint"
        class="fixed left-0 top-0 -z-50"
        @update:visible="showPrescriptionPrint = false"
      />
    </Teleport>
  </div>
</template>
