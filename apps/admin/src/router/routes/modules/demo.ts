import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      icon: 'lucide:flask-conical',
      order: 9997,
      title: '演示功能',
    },
    name: 'Demo',
    path: '/demo',
    children: [
      {
        name: 'KeyboardNavigation',
        path: '/demo/keyboard-navigation',
        component: () => import('#/views/demo/keyboard-navigation/index.vue'),
        meta: {
          icon: 'lucide:keyboard',
          title: 'VXE-Table 键盘导航',
        },
      },
      {
        name: 'DemoTable',
        path: '/demo/demo',
        component: () => import('#/views/demo/demo/index.vue'),
        meta: {
          icon: 'lucide:table',
          title: '测试单表',
        },
      },
      {
        name: 'DemoTree',
        path: '/demo/tree',
        component: () => import('#/views/demo/tree/index.vue'),
        meta: {
          icon: 'lucide:tree-pine',
          title: '测试树表',
        },
      },
    ],
  },
];

export default routes;
