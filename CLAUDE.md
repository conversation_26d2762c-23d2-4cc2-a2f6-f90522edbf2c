# CLAUDE.md
* 你是 Claude
* 遵循 KISS 原则，非必要不要过度设计
* 实现简单可维护，不需要考虑太多防御性的边界条件
* 你需要逐步进行，通过多轮对话来完成需求，进行渐进式开发
* 在开始设计方案或实现代码之前，你需要进行充分的调研。如果有任何不明确的要求，请在继续之前向我确认
* 当你收到一个需求时，首先需要思考相关的方案，并请求我进行审核。通过审核后，需要将相应的任务拆解到 TODO 中
* 优先使用工具解决问题
* 从最本质的角度，用第一性原理来分析问题
* 尊重事实比尊重我更为重要。如果我犯错，请毫不犹豫地指正我，以便帮助我提高

This file provides guidance to Claude Code (claude.ai/code) when working with code in this repository.

## Project Overview

This is a **Clinic Information System (CIS)** built on Vue Vben Admin v5.0 - a Vue 3 + TypeScript admin template. It's a monorepo managing a comprehensive medical clinic management platform with modules for patient management, medical records, pharmacy operations, inventory tracking, and payment processing.

## Architecture

**Monorepo Structure:**
- `apps/admin/` - Main clinic management application
- `packages/` - Shared packages and UI components
- `internal/` - Internal tooling and configuration
- Built with Vue 3, TypeScript, Ant Design Vue, and Vite

**Core Technologies:**
- Vue 3 + Composition API
- TypeScript for type safety
- Ant Design Vue for UI components
- VXE Table for complex data tables
- Pinia for state management
- Vue Router for routing
- Vite for build tooling
- PNPM for package management
- Turbo for monorepo orchestration

## Development Commands

**Setup:**
```bash
pnpm install
```

**Development:**
```bash
pnpm dev                    # Start all development servers
pnpm dev:antd               # Start main admin app only
```

**Building:**
```bash
pnpm build                  # Build all applications
pnpm build:antd             # Build main admin app only
```

**Code Quality:**
```bash
pnpm lint                   # Run ESLint
pnpm format                 # Format code with Prettier
pnpm check:type             # TypeScript type checking
pnpm check                  # Run all checks (circular deps, type check, spell check)
```

**Testing:**
```bash
pnpm test:unit              # Run unit tests with Vitest
pnpm test:e2e               # Run end-to-end tests
```

## Key Development Patterns

### Clinic Domain Architecture

The clinic features are organized by medical workflow:

- **Patient Management** (`src/views/clinic/patient/`) - Patient registration, demographics, medical history
- **Medical Records** (`src/views/clinic/medical/record/`) - Clinical notes, diagnoses, treatment plans  
- **Outpatient Services** (`src/views/clinic/outpatient/attend/`) - Appointment scheduling, visit management
- **Pharmacy** (`src/views/clinic/pharmacy/`) - Drug management, prescription fulfillment, dispensing
- **Inventory** (`src/views/clinic/inventory/`) - Stock management, receiving, distribution
- **Payment** (`src/views/clinic/payment/`) - Billing, payment processing, financial records

### Component Structure

**Clinic Components** (`src/components/clinic/`):
All clinic-specific components follow consistent patterns:
- Card components for data display (e.g., `medical-card`, `prescription-print`)
- Table components for data grids (e.g., `medical-record-table`, `medicine-prescription-table`)
- Selection modals for entity picking (e.g., `drug-select`, `pharmacy-select`)

### API Architecture

APIs are organized by domain in `src/api/clinic/`:
- Each module has `index.ts` (API functions) and `model.d.ts` (TypeScript interfaces)
- Uses consistent patterns for CRUD operations
- Proxy configuration: `/api` → `http://localhost:7454` (for development)

### Medical Workflow Enums

Key business logic uses enums in `src/constants/qili-common-enum.ts`:
- `MedicineCategory`: CY (草药/Chinese herbs), XY (西药/Western medicine), ZCY (中成药/Chinese patent medicine)
- `MedicalRecordStatus`: Patient visit workflow states (WAITING → IN_PROGRESS → PRESCRIPTION_DONE → DISPENSING → COMPLETED)

### Common Utilities

**CIS-specific utilities** (`src/utils/cis-common.ts`):
- `isTrue()` - Boolean evaluation for various data types
- `validTableEditRules()` - Table validation with Chinese error messages
- `priceToYuan()` - Currency conversion from cents to yuan

## Important File Locations

- **Main app entry**: `apps/admin/src/main.ts`
- **Type definitions**: `apps/admin/src/api/clinic/*/model.d.ts`
- **Shared constants**: `apps/admin/src/constants/qili-*.ts`
- **Clinic components**: `apps/admin/src/components/clinic/`
- **Views**: `apps/admin/src/views/clinic/`
- **Vite config**: `apps/admin/vite.config.mts`

## Code Conventions

Based on the cursor rules in `.cursor/rules/cursor-rules.mdc`, follow these patterns:
- Use the MCP "寸止" tool when requirements are unclear or when multiple approaches exist
- Ask for clarification before making assumptions
- Maintain memory of project context using the git root directory path
- Focus on the specific task without creating unnecessary documentation, tests, or builds unless explicitly requested

**Vue/TypeScript Conventions:**
- Use Composition API with `<script setup>` syntax
- Follow existing component patterns in the clinic modules
- Maintain type safety with proper TypeScript interfaces
- Use Ant Design Vue components consistently
- Follow the drawer/modal patterns established in existing components

## Environment Setup

- **Node.js**: >=20.10.0
- **PNPM**: >=9.12.0 (enforced by `preinstall` script)
- **Dev server**: Vite with HMR
- **API proxy**: Development requests to `/api` are proxied to `localhost:7454`

## Common Tasks

1. **Adding new clinic features**: Follow the established pattern in `src/views/clinic/` with corresponding API, components, and types
2. **Table components**: Use VXE Table with Ant Design Vue integration via `@vxe-ui/plugin-render-antd`
3. **Form validation**: Use the `validTableEditRules()` utility for consistent error handling
4. **State management**: Use Pinia stores for complex state, local reactive state for simple components
5. **Internationalization**: Add translations to `src/locales/langs/zh-CN/` files

This is a production medical system, so maintain high code quality, proper error handling, and data validation.
